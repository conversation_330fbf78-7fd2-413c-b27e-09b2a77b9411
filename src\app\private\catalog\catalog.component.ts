import { ChangeDetectionStrategy, ChangeDetectorRef, Component, CUSTOM_ELEMENTS_SCHEMA, ElementRef, inject, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { IonContent, LoadingController, Platform, ToastController } from '@ionic/angular';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { HybridDbService } from 'src/app/shared/hybrid-db.service';
import { CatalogCard, CatalogCardType } from 'src/app/service/data/catalog-card';
import { LocalStorageService } from 'src/app/service/local-storage.service';
import { CommonModule, Location } from '@angular/common';
import { animate, style, transition, trigger } from '@angular/animations';
import { Favorite } from 'src/app/service/data/favorite';
import { Category } from 'src/app/service/data/category';
import { select, Store } from '@ngrx/store';
import { setCategories } from 'src/app/store/actions/categories.actions';
import Utils from 'src/app/shared/utils';
import { setItems4view } from 'src/app/store/actions/catalog-view.actions';
import { addSelection, removeLastSelection, resetNavStack, reverseSelection } from 'src/app/store/actions/nav-stack.actions';
import { addNavHistory, removeLastNavHistory, resetNavHistory } from 'src/app/store/actions/nav-history.actions';
import { Selection } from 'src/app/shared/selection';
import { Product } from 'src/app/service/data/product';
import { Cart } from 'src/app/service/data/cart';
import { setCurrentCustomer } from 'src/app/store/actions/current-customer.actions';
import { Customer } from 'src/app/service/data/customer';
import { resetCart, setCart } from 'src/app/store/actions/cart.actions';
import { removeLastRoutingStack } from 'src/app/store/actions/routing-stack.actions';
import { CardType, CatalogService, ViewType } from 'src/app/service/catalog/catalog.service';
import { SwiperContainer } from 'swiper/element';
import { ScreenOrientation } from '@capacitor/screen-orientation';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { SearchComponent } from '../search/search.component';
import { IonicModule } from '@ionic/angular';
import { NavigationQueueComponent } from './navigation-queue/navigation-queue.component';
import { SmallCatalogCardComponent } from './catalog-card/small-catalog-card/small-catalog-card.component';
import { LargeCatalogCardComponent } from './catalog-card/large-catalog-card/large-catalog-card.component';
import { MediumCatalogCardComponent } from './catalog-card/medium-catalog-card/medium-catalog-card.component';
import { Price } from './catalog-card/catalog-card.component';
import { InfiniteCatalogCardComponent } from './catalog-card/infinite-catalog-card/infinite-catalog-card.component';
import { NavigationPanelComponent } from './navigation-panel/navigation-panel.component';
import { SubcategoriesPreviewComponent } from './subcategories-preview/subcategories-preview.component';
import { PricesComponent } from './product/prices/prices.component';
import { addIcons } from 'ionicons';
import { chevronBackOutline, chevronForwardOutline, caretBackCircle, caretForwardCircle, } from 'ionicons/icons';
import { RightCatalogToolbarComponent } from './right-catalog-toolbar/right-catalog-toolbar.component';
export const INFINITE_CARD_NUMBER = 48;
import { IonicSlides } from '@ionic/angular';
import { PaginatedCatalogComponent } from "./paginated-catalog/paginated-catalog.component";
import { InfiniteCatalogComponent } from './infinite-catalog/infinite-catalog.component';
import { Capacitor } from '@capacitor/core';
import { NavigationService } from 'src/app/service/navigation/navigation.service';
import { SyncroUnifiedService } from 'src/app/service/syncro-unified/syncro-unified.service';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { AssignTagDialogComponent } from './assign-tag-dialog/assign-tag-dialog.component';

@Component({
  selector: 'app-catalog',
  templateUrl: './catalog.component.html',
  styleUrls: ['./catalog.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  animations: [
    trigger('datacolPopup', [
      transition('void => *', [
        style({ opacity: 0 }),
        animate(200, style({ opacity: 1 }))
      ]),
      transition('* => void', [
        animate(200, style({ opacity: 0 }))
      ])
    ])
  ],
  standalone: true,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [
    CommonModule,
    IonicModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule,
    MatDialogModule,
    SearchComponent,
    NavigationQueueComponent,
    NavigationPanelComponent,
    SubcategoriesPreviewComponent,
    PricesComponent,
    RightCatalogToolbarComponent,
    PaginatedCatalogComponent,
    InfiniteCatalogComponent,
    AssignTagDialogComponent
  ]
})
export class CatalogComponent implements OnInit {

  protected catalogService = inject(CatalogService);
  protected navigationService = inject(NavigationService);
  private _syncroUnifiedService = inject(SyncroUnifiedService);

  swiperModules = [IonicSlides];
  @ViewChild('slides', { static: false }) slides: SwiperContainer;
  sliderOpacity: number = 1;
  navigationType = (this._lsService.get('catalog_navigation_type') || 'P') as 'P' | 'I';
  customerName: string = '';
  showFavorites: boolean = false;
  isFilterShown: boolean = false;
  items4view: CatalogCard[] = [];
  items4Infinite: CatalogCard[] = [];
  itemsByPages: CatalogCard[][] = [];
  favorites: Favorite[] = [];
  categoryToOpenFromNavbar: string | null = null;
  navigationQueue$ = this._store.pipe(select('navStack'));
  prevCategory: Selection = null;
  nextCategory: Selection = null;
  currentPageIndex = 0;
  totalPages = 10;
  cardDimension: { columns: number, rows: number } = { columns: 4, rows: 3 };
  subcategories4Preview: { idParent: string, name: string, id: string }[] = [];
  isSubcatPreviewShown: boolean = false;
  isPricesShown: boolean = false;
  selProducts: Product[] | [] = [];
  selDatacolCategoryName: string = '';
  showNavigator: boolean = false;
  cardView: CardType = (!!localStorage.getItem('cardViewType') ? localStorage.getItem('cardViewType') as CardType : CardType.SMALL);
  loading;
  nextRootCategory: { current: Category, next: Category } | null = null;
  private temporaryItem: CatalogCard[] = []; // appoggio tutti gli item che dovrebbe avere la vista corrente, poi in visualizzazione li carico invece un po' alla volta
  private screenWidth = 0;
  private screenHeight = 0;
  private cardForPage = this.cardDimension.rows * this.cardDimension.columns;
  private CARD_GAP = 6 //* window.devicePixelRatio;
  private CARD_WIDTH = 300 //* window.devicePixelRatio;
  private CARD_HEIGHT = 120 //* window.devicePixelRatio;
  private dcProperty = [];
  wait = true;
  activeRootCategory: any;
  @ViewChild(IonContent, { static: false }) ionContent!: IonContent;
  @ViewChild('infiniteGrid', { read: ElementRef }) infiniteGrid!: ElementRef;

  // INJECTED SERVICES
  private _platform = inject(Platform);
  private _router = inject(Router);
  private _location = inject(Location);
  private _activatedRoute = inject(ActivatedRoute);
  private _cdr = inject(ChangeDetectorRef);
  private _dbService = inject(HybridDbService);
  private _translate = inject(TranslateService);
  private _loadingController = inject(LoadingController);
  private _toastController = inject(ToastController);

  constructor(private _lsService: LocalStorageService, private _store: Store<any>, private _dialog: MatDialog) {
    addIcons({ chevronBackOutline, chevronForwardOutline, caretBackCircle, caretForwardCircle });

    const physicalScreenWidth = window.screen.width //* window.devicePixelRatio;
    const physicalScreenHeight = window.screen.height //* window.devicePixelRatio;
    this.screenWidth = physicalScreenWidth;
    this.screenHeight = physicalScreenHeight;

    // Controllo per sicurezza...
    if ((window.matchMedia("(orientation: landscape)").matches && physicalScreenHeight > physicalScreenWidth) ||
      (window.matchMedia("(orientation: portrait)").matches && physicalScreenWidth > physicalScreenHeight)) {
      this.screenWidth = physicalScreenHeight;
      this.screenHeight = physicalScreenWidth;
    }

    this._platform.ready().then(() => {
      this._platform.resume.subscribe(() => {
        this.setRowsAndColumns();
      });
    });
  }

  async ngOnInit() {
    await this._dbService.getAll(["categories"]).then(async (data) => {
      console.log('Categories loaded:', data);
      
      await this._store.dispatch(setCategories({ items: data }));
      this.catalogService.setCatalogsLoaded(true);
      this._cdr.detectChanges();
    });
    
    await this._platform.ready();
    this.loading = await this._loadingController.create({
      message: this._translate.instant('GENERICS.WAIT'),
    });

    // Initialize the view type in the service from localStorage
    const storedNavigationType = this._lsService.get('catalog_navigation_type') as ViewType;
    console.log(`Stored navigation type: ${storedNavigationType}`);
    if (storedNavigationType) {
      this.catalogService.setViewType(storedNavigationType);
    } else {
      this.catalogService.setViewType(ViewType.PAGINATED);
    }

    

    console.log(this._platform.is('capacitor'));
    if (Capacitor.isNativePlatform()) {
      ScreenOrientation.addListener('screenOrientationChange', () => {
        [this.screenWidth, this.screenHeight] = [this.screenHeight, this.screenWidth];

        if (this.catalogService.isInfiniteView) {
          this.calculateCardPerRow();
        } else {
          this.drawGrid(true);
        }
      });
    }

    this.loadCustomerData();
  }

  

 

 

 
  private async loadCustomerData() {
    if (!this.catalogService.isProspect) {
      this._lsService.remove("infoDataList_customer");
      this._lsService.remove("customerAgentCode");
      // Precarico anche il record infoDataList che servirà poi per i prezzi
      await this._dbService.getRecordsByANDCondition("customers", [{ key: 'uid', value: this.catalogService.userUid }]).then((data) => {
        if (data && data[0]) {
          const customer = data[0] as Customer;
          if (customer.agent) {
            this._lsService.set("customerAgentCode", customer.agent);
          }
          if (customer.infoDataList) {
            this._lsService.set("infoDataList_customer", JSON.stringify(Utils.objToJson(customer.infoDataList)));
          }
        }
      });
      let cart = { idCustomer: null, idCart: null, currentQuantityInCart: 0 };
      this._store.pipe(select('cart')).subscribe(res => cart = res).unsubscribe();
      if (!cart.idCustomer || !cart.idCart || cart.idCustomer !== this.catalogService.userUid) {
        this._store.dispatch(resetCart());
        await this._dbService.getRecordsByANDCondition('carts', [{ key: 'agentCode', value: this._lsService.get("customerAgentCode") }, { key: 'idCustomer', value: this.catalogService.userUid }]).then(async (data: Cart[]) => {
          if (!!data[0] && !!data[0]['id']) {
            const rows = data[0];
            if (!!rows.products) {
              const products = Utils.objToJson(rows.products);
              if (!!products) {
                /* const quantity = products.reduce((result, item) => {
                  return result + item.quantity;
                }, 0); */
                this._store.dispatch(setCart({ cart: { idCustomer: this.catalogService.userUid, idCart: rows.id, currentQuantityInCart: products.length } }));
              }
            }
          }
        });
      }
    } else {
      this._store.dispatch(resetCart());
      this._lsService.remove("infoDataList_customer");
      this._lsService.remove("customerAgentCode");
    }
  }

  private drawGrid(withChange: boolean): Promise<void> {
    this.cardView = (!!localStorage.getItem('cardViewType') ? localStorage.getItem('cardViewType') as CardType : CardType.SMALL);
    if (withChange) {
      this._cdr.detectChanges();
    }
    const comeFromDc = this.items4view.length > 0 ? this.items4view[this.currentPageIndex * this.cardForPage].id : null;
    this.setRowsAndColumns(); // Setta righe e colonne e card per pagina in base alla risoluzione dello schermo
    if (withChange) {
      this._cdr.detectChanges();
      this._cdr.markForCheck();
    }
    if (withChange && this.items4view.length > 0 && !!comeFromDc) {
      let navHistory = [];
      this._store.pipe(select('navHistory')).subscribe(res => navHistory = res).unsubscribe();
      if (navHistory.length > 0) {
        const categoryToOpen = navHistory[navHistory.length - 1].item;
        const idRootCategory = this.items4view[0].idRootCategory;

        categoryToOpen.path.forEach((idCategory, index) => {
          this.openCategory({ id: idCategory, type: 'CATEGORY', idRootCategory: idRootCategory }, true).then(_ => {
            if (categoryToOpen.path.length === index + 1) {
              if (!!comeFromDc) {
                setTimeout(() => {
                  const cardIndex = this.items4view.findIndex(item => item.id.toString() === comeFromDc.toString());
                  const page = Math.floor(cardIndex / this.cardForPage) + 1;
                  this.goToPage(page - 1); // le pagine iniziano da zero
                }, 1000);
              }
            }
          })
        });
      }
      return null;
    }
    else
      return this.getAllCategories();
  }

  private setRowsAndColumns() {
    if (this.catalogService.isInfiniteView && !this.catalogService.isRootView) {
      this.cardView = CardType.INFINITE;
      this.cardForPage = INFINITE_CARD_NUMBER
      this.calculateCardPerRow();
    } else if (this.catalogService.isRootView || this.cardView === 'small') {
      const height = this.screenHeight - (220 /* window.devicePixelRatio*/); // tolgo header, footer, paginator, padding vari
      const width = this.screenWidth - (50 /* window.devicePixelRatio*/);
      this.cardDimension.columns = Math.floor(width / (this.CARD_WIDTH + this.CARD_GAP));
      this.cardDimension.rows = Math.floor(height / this.CARD_HEIGHT);
      this.cardForPage = this.cardDimension.rows * this.cardDimension.columns;
    } else if (this.cardView === 'medium') {
      this.cardDimension.columns = 2;
      this.cardDimension.rows = 2;
      this.cardForPage = this.cardDimension.rows * this.cardDimension.columns;
    } else if (this.cardView === 'large') {
      this.cardDimension.columns = 2;
      this.cardDimension.rows = 1;
      this.cardForPage = this.cardDimension.rows * this.cardDimension.columns;
    }
  }

  private async getAllCategories() {
    // TODO: inserire l'elaborazione del json per la vista infinite-scrolling
    try {
      // Inizializzo l'array delle categorie
      let categories = [];

      // Recupero le categorie dallo store e le assegno all'array 'categories'
      this._store.pipe(select('categories')).subscribe(res => categories = [...res]).unsubscribe();

      // Se l'array dcProperty è vuoto, lo popolo dallo store
      this.dcProperty.length === 0 && this._store.pipe(select('dcProperty')).subscribe(res => this.dcProperty = [...res]).unsubscribe();


      // Resetto le variabili prevCategory e nextCategory
      this.prevCategory = null;
      this.nextCategory = null;

      // Se ci sono categorie disponibili
      if (categories.length > 0) {
        // Se non sono nella modalità "preferiti"
        if (!this.showFavorites) {
          // Mappo le categorie in oggetti CatalogCard usando il nuovo metodo fromCategory
          this.temporaryItem = categories.map((item: Category) => {
            const card = CatalogCard.fromCategory(item, 'CATEGORY', this.isFavorite(item.id.toString()));

            // Aggiorna i campi specifici per la visualizzazione
            card.focus = this.dcProperty.filter(f => f.idSubCategory.toString() === item.id.toString() && f.focus == 'S').length > 0 ? 'S' : 'N';
            card.divisionStatusCode = this.dcProperty.filter(f => f.idSubCategory.toString() === item.id.toString() && f.divisionStatusCode == 'Z3').length > 0 ? 'Z3' : '';

            return card;
          });

          // Assegno la lista temporanea degli item a items4view
          this.items4view = this.temporaryItem;
          this._cdr.markForCheck();

          // Preparo la visualizzazione per pagine e scrolling infinito
          if (!this.catalogService.isRootView && this.cardView == CardType.INFINITE) {
            this.prepareCardForInfiniteScroll();
          }
          else {
            this.prepareCardByPage();
          }

          // Se ci sono slide, imposto lo slide corrente alla prima posizione
          if (this.slides) {
            this.slides.swiper.slideTo(0, 0);
          }
        } else {
          // Se sono nella modalità "preferiti"
          this._store.pipe(select('favorites')).subscribe(res => this.favorites = [...res].filter((item: Favorite) => item.customerUid === this.catalogService.userUid)).unsubscribe();

          // Se non ci sono preferiti
          if (!!this.favorites && this.favorites.length === 0) {
            this.totalPages = 0;
            this.items4view = [];
            this.wait = false;

            // Aggiungo in testa la card dei preferiti (vuota)
            this.items4view = [this.createFavoritesCard()];
          } else {
            // Trasformo i favoriti in JSON e li visualizzo
            this.items4view = this.favorites.map((item) => {
              return Utils.objToJson(item.catalogCard);
            });

            // Aggiungo la card dei preferiti in testa
            this.items4view = [this.createFavoritesCard()].concat(this.items4view);
          }

          // Imposto che il caricamento è terminato
          this.wait = false;

          // Preparo la visualizzazione delle card a pagine
          this.prepareCardByPage();

          // Imposto la navigazione come 'P' (piatta)
          this.catalogService.setViewType(ViewType.PAGINATED);

          // Se ci sono slide, imposto lo slide corrente alla prima posizione
          if (this.slides)
            this.slides.swiper.slideTo(0, 0);

          // Resetto le variabili prevCategory e nextCategory
          this.prevCategory = null;
          this.nextCategory = null;
        }
      } else {
        // Se non ci sono categorie nello store, le recupero dal database locale
        await this._dbService.getAll(["categories"], ['id', 'name', 'image', 'isProduct', 'subcategories']).then((data) => {
          if (data.length == 0) {
            // Se non ci sono dati nel database, resetto le pagine
            this.totalPages = 0;
            if (this.slides)
              this.slides.swiper.slideTo(0, 0);
          } else {
            // Altrimenti, assegno i dati a categories e aggiorno lo store
            categories = data;
            this._store.dispatch(setCategories({ items: categories }));

            // Se non sono nella modalità "preferiti", visualizzo le categorie
            if (!this.showFavorites) {
              this.items4view = categories.map((item: Category) => {
                const card = CatalogCard.fromCategory(item, 'CATEGORY', this.isFavorite(item.id.toString()));

                // Aggiorna i campi specifici per la visualizzazione
                card.focus = this.dcProperty.filter(f => f.idSubCategory.toString() === item.id.toString() && f.focus == 'S').length > 0 ? 'S' : 'N';
                card.divisionStatusCode = this.dcProperty.filter(f => f.idSubCategory.toString() === item.id.toString() && f.divisionStatusCode == 'Z3').length > 0 ? 'Z3' : '';

                return card;
              });

              // Preparo la visualizzazione per pagine e scrolling infinito
              if (this.cardView == 'infinite') {
                this.prepareCardForInfiniteScroll();
              }
              else {
                this.prepareCardByPage();
              }

              // Se ci sono slide, imposto lo slide corrente alla prima posizione
              if (this.slides)
                this.slides.swiper.slideTo(0, 0);
            }
          }
        }).finally(() => {
          // Quando tutto è finito, rimuovo il caricamento
          // this.loading.dismiss();
        });
      }
    } catch (error) {
      // Gestisco eventuali errori
      this.loading.dismiss();
    }
  }

  private createFavoritesCard() {
    return new CatalogCard(null, null, this.customerName, null, null, 'FAVORITES', null, null, null, null);
  }

  private isFavorite(idCategory: string) {
    const favorites: Favorite[] = this.favorites.filter((favorite) => favorite.customerUid.toString() === this.catalogService.userUid.toString() && favorite.idCategory.toString() === idCategory);
    const found = favorites.length === 1;
    return found;
  }

  async slideChanges(event) {
    console.log('slideChanges', event);
    let previous = this.slides.swiper.previousIndex

    this.currentPageIndex = this.slides.swiper.activeIndex;

    const pagination = document.getElementsByClassName("pagination");
    const currentPage = document.getElementsByClassName("current");
    if (pagination.length > 0 && currentPage.length > 0) {
      // pagination[0].scrollLeft = index*38; // dove 38 è la dimensione del blocchetto del numero pagina
      if (this.currentPageIndex > previous)
        pagination[0].scrollLeft = currentPage[0].parentElement.offsetLeft
      else if (this.currentPageIndex === 0) {
        pagination[0].scrollLeft = 0;
      }
      else {
        pagination[0].scrollLeft = currentPage[0].parentElement.offsetLeft - 43;
      }
    }
    this.itemsByPages = this.creaArrayPaginato(this.items4view, this.cardForPage, this.currentPageIndex);
    this.setNavbarWhenSlidePage(this.currentPageIndex);
  }

  private prepareCardByPage() {
    this.wait = false;
    this.itemsByPages = this.creaArrayPaginato(this.items4view, this.cardForPage, 0);
    this._cdr.detectChanges();
    this._cdr.markForCheck();
    this.sliderOpacity = 1;
    //this.creaArrayPaginato(this.items4view, this.cardForPage, 0);
    this.setTotalPages();
  }

  infiniteIndex: number = 0
  prepareCardForInfiniteScroll() {
    console.log('prepareCardForInfiniteScroll called', {
      isRootView: this.catalogService.isRootView,
      items4viewLength: this.items4view?.length || 0,
      cardForPage: this.cardForPage
    });

    if (!this.catalogService.isRootView) {
      this.cardForPage = INFINITE_CARD_NUMBER;
    }

    if (this.items4view && this.items4view.length > 0) {
      this.creaArrayInfinito(this.items4view, this.cardForPage, 'init');
    } else {
      console.warn('prepareCardForInfiniteScroll: items4view is empty or undefined');
      this.items4Infinite = [];
    }
  }

  private setTotalPages() {
    this.totalPages = this.itemsByPages.length;
    console.log('totalPages', this.totalPages);
    this._cdr.detectChanges();
    this._cdr.markForCheck();
  }

  trackByFn(index: number, item: CatalogCard): string {
    return item.id;
  }

  private creaArrayPaginato(arrayOriginale, elementiPerPagina, paginaCorrente) {
    var arrayPaginato = [];
    for (var i = 0; i < arrayOriginale.length; i++) {
      var pageIndex = Math.floor(i / elementiPerPagina);
      if (pageIndex === paginaCorrente || pageIndex === paginaCorrente - 1 || pageIndex === paginaCorrente + 1) {
        if (!arrayPaginato[pageIndex]) {
          arrayPaginato[pageIndex] = [];
        }
        arrayPaginato[pageIndex].push(arrayOriginale[i]);
      } else {
        if (!arrayPaginato[pageIndex]) {
          arrayPaginato[pageIndex] = [];
        }
        arrayPaginato[pageIndex].push({});
      }
    }

    return arrayPaginato;
  }

  goToPage(pageIndex, hideLoader = true) {
    if (this.slides) {
      this.loading.present();

      const pagination = document.getElementsByClassName("pagination");
      const currentPage = document.getElementsByClassName("current");
      if (pagination.length > 0 && currentPage.length > 0) {
        // pagination[0].scrollLeft = index*38; // dove 38 è la dimensione del blocchetto del numero pagina
        pagination[0].scrollLeft = currentPage[0].parentElement.offsetLeft
      }
      this.itemsByPages = this.creaArrayPaginato(this.items4view, this.cardForPage, pageIndex);
      this.slides.swiper.slideTo(pageIndex, 0);
      this.currentPageIndex = pageIndex;
      this.sliderOpacity = 1;
      this._cdr.markForCheck();
    }
    if (hideLoader) {
      this.loading.dismiss();
    }
  }

  getNextCategory(currentIndex: number) {
    let nextCategory = null;
    let categories = [];
    this._store.pipe(select('categories')).subscribe(res => categories = res).unsubscribe()

    if (currentIndex < categories.length - 1) {
      nextCategory = categories[currentIndex + 1];
    } else {
      nextCategory = categories[0];
    }
    return nextCategory;
  }

  private setNavbarWhenSlidePage(pageIndex) {
    var filtered: CatalogCard[] = [];
    var identified: CatalogCard | null = null;
    if (this.catalogService.isPaginatedView && this.cardView !== "small") {
      const firstCard = this.itemsByPages[pageIndex][0];
      const firstProductIndex = this.temporaryItem.findIndex(item => item.id.toString() === firstCard.id.toString());
      for (let i = firstProductIndex; i >= 0; i--) {
        if (this.temporaryItem[i].type === 'CATEGORY') {
          identified = this.temporaryItem[i];
          break;
        }
      }
    } else {
      const page = this.temporaryItem.slice(pageIndex * this.cardForPage, pageIndex * this.cardForPage + this.cardForPage);
      filtered = page.filter(item => !item.isProduct);
      if (filtered.length > 0) {
        identified = filtered[0];
      }
    }
    if (!!identified && !this.catalogService.isRootView) {
      let navStack = [];
      this.navigationQueue$.subscribe(res => navStack = [...res]).unsubscribe();
      if (navStack.length > 1)
        this._store.dispatch(removeLastSelection());
      this._store.dispatch(addSelection({ item: { name: identified.name, id: identified.id.toString(), isProduct: false, idRootCategory: identified.idRootCategory } }));
    }
  }

  async openCategory(clicked: { id: string, type: CatalogCardType, idRootCategory: string | null }, fromBack: boolean, forceChangeRoot: boolean = false) {

    this.navigationService.navigateToCategory(clicked.id, clicked.idRootCategory);
    console.log('openCategory called with:', { clicked, fromBack, forceChangeRoot });

    console.log('Current state:', {
      navigationType: this.catalogService.isInfiniteView ? 'I' : 'P',
      items4Infinite: this.items4Infinite,
      navStack: this._store.pipe(select('navStack'))
    });

    // Mostra l'indicatore di caricamento
    this.showNextCategoryButton = false;

    // In modalità infinita, se stiamo cliccando su una categoria e non è una root view e non è forceChangeRoot,
    // allora non fare nulla (questo è per evitare di aprire sottocategorie in modalità infinita)
    if (clicked.type === 'CATEGORY' && this.catalogService.isInfiniteView && !forceChangeRoot && !this.catalogService.isRootView) {
      console.log('Skipping category open due to infinite navigation conditions');
      return;
    }

    this.loading.present();

    // Se la navigazione è piatta, non è la vista radice e l'elemento cliccato è una categoria, non fare nulla
    if (!(this.catalogService.isInfiniteView && forceChangeRoot) && (this.catalogService.isPaginatedView && !this.catalogService.isRootView && clicked.type === 'CATEGORY' && !forceChangeRoot)) {
      console.log('Skipping category open due to flat navigation conditions');
      this.loading.dismiss();
      return;
    }
    else if (clicked.type === 'CATEGORY') {
      // Se è una categoria, configura la vista
      this.catalogService.setRootView(false);
      this.setRowsAndColumns(); // Imposta righe e colonne

      try {
        let categories = [];
        // Recupera le categorie dallo store
        this._store.pipe(select('categories')).subscribe(res => categories = res).unsubscribe();

        // Se dcProperty è vuoto, recuperalo dallo store
        this.dcProperty.length === 0 && this._store.pipe(select('dcproperty')).subscribe(res => this.dcProperty = [...res]).unsubscribe();

        {
          // Gestione per navigazione diversa dalla 'C'
          var clickedCategory = null;
          let catalog = [];
          this._store.pipe(select('catalogByProduct')).subscribe(res => catalog = [...res]).unsubscribe();

          // Verifica se stiamo cliccando su una root category per mostrare le sue categorie figlie
          const clickedCategoryFromDb = await this.getClickedCategory(clicked.id);

          if (clickedCategoryFromDb && this.isRootCategory(clickedCategoryFromDb)) {
            console.log(`🔍 Click su root category (navigazione non-C): ${clickedCategoryFromDb.name} (ID: ${clickedCategoryFromDb.id})`);
            console.log(`Current state: isRootView=${this.catalogService.isRootView}, isInfiniteView=${this.catalogService.isInfiniteView}`);

            // Se siamo nella vista root, naviga usando URL per una migliore esperienza utente
            if (this.catalogService.isRootView) {
              console.log(`🔄 Navigazione URL verso categorie figlie della root category: ${clickedCategoryFromDb.id}`);
              this.loading.dismiss(); // Dismissi il loading prima della navigazione
              this.navigateToRootCategoryChildren(clickedCategoryFromDb.id.toString());
              return;
            }

            // Altrimenti, carica le categorie figlie direttamente
            const childCategories = await this._syncroUnifiedService.getCategoriesByParent(clickedCategoryFromDb.id);
            console.log(`📦 Trovate ${childCategories.length} categorie figlie per la root category ${clickedCategoryFromDb.name}`);

            if (childCategories.length > 0) {
              // Mappa le categorie figlie su CatalogCard usando il nuovo metodo fromCategory
              this.temporaryItem = childCategories.map((item: Category) => {
                const card = CatalogCard.fromCategory(item, Utils.objToJson(item.isProduct) ? 'PRODUCT' : 'CATEGORY', this.isFavorite(item.id.toString()));

                // Imposta la root category come idRootCategory
                card.idRootCategory = clickedCategoryFromDb.id;

                // Aggiorna i campi specifici per la visualizzazione
                card.focus = this.dcProperty.filter(f => f.idSubCategory.toString() === item.id.toString() && f.focus == 'S').length > 0 ? 'S' : 'N';
                card.divisionStatusCode = this.dcProperty.filter(f => f.idSubCategory.toString() === item.id.toString() && f.divisionStatusCode == 'Z3').length > 0 ? 'Z3' : '';

                return card;
              });
              console.log(`✅ temporaryItem populated with ${this.temporaryItem.length} items from childCategories for root category ${clickedCategoryFromDb.name}`);

              // Imposta clickedCategory con i dati dal database
              clickedCategory = {
                id: clickedCategoryFromDb.id,
                name: clickedCategoryFromDb.name,
                isProduct: clickedCategoryFromDb.isProduct,
                idRootCategory: clickedCategoryFromDb.id // La root category è il suo stesso idRootCategory
              };
            }
          } else {
            // Comportamento normale per categorie non-root
            if (forceChangeRoot || this.items4view.length === 0) {
              clickedCategory = catalog.find((item) => item.rootId.toString() === clicked.id.toString());
            } else {
              clickedCategory = this.items4view.find((item) => item.id.toString() === clicked.id.toString());
            }

            if (typeof clickedCategory == "undefined") {
              clickedCategory = this.items4view.find((item) => item.id.toString() === clicked.idRootCategory.toString());
            }

            var filtered = catalog.filter((item: { rootId: string, items: Category[] }) => item.rootId.toString() === clicked.id.toString());
            if (filtered.length == 0) {
              filtered = catalog.filter((item: { rootId: string, items: Category[] }) => item.rootId.toString() === clicked.idRootCategory.toString());
            }
            if (!!filtered && filtered.length > 0) {
              this.temporaryItem = filtered[0].items.map((item: Category) => {
                const card = CatalogCard.fromCategory(item, Utils.objToJson(item.isProduct) ? 'PRODUCT' : 'CATEGORY', this.isFavorite(item.id.toString()));

                // Imposta l'idRootCategory
                card.idRootCategory = (!!clicked.idRootCategory ? clicked.idRootCategory : clicked.id);

                // Aggiorna i campi specifici per la visualizzazione
                card.focus = this.dcProperty.filter(f => f.idSubCategory.toString() === item.id.toString() && f.focus == 'S').length > 0 ? 'S' : 'N';
                card.divisionStatusCode = this.dcProperty.filter(f => f.idSubCategory.toString() === item.id.toString() && f.divisionStatusCode == 'Z3').length > 0 ? 'Z3' : '';

                return card;
              });
            }
          }

          // Gestione comune per entrambi i casi
          var currentIndex = categories.findIndex(item => item.id.toString() === clicked.id.toString());
          if (currentIndex == -1)
            currentIndex = categories.findIndex(item => item.id.toString() === clicked.idRootCategory.toString());
          if (this.cardView == 'infinite') {
            const next = this.getNextCategory(currentIndex);
            this.nextRootCategory = {
              current: categories[currentIndex],
              next: next
            };
          } else {
            this.nextRootCategory = {
              current: categories[currentIndex],
              next: (categories.length > (currentIndex + 1)) ? this.items4view[currentIndex + 1] : categories[0]
            };
          }

          if (forceChangeRoot) {
            this.items4Infinite = [];
          }

          // Prepara la vista a seconda del tipo di card (infinito o a pagina)
          console.log('Preparing view with temporaryItem', {
            temporaryItemLength: this.temporaryItem?.length || 0,
            isInfiniteView: this.catalogService.isInfiniteView,
            isRootView: this.catalogService.isRootView,
            cardView: this.cardView,
            forceChangeRoot
          });

          if (this.temporaryItem && this.temporaryItem.length > 0) {
            if (this.catalogService.isInfiniteView && !this.catalogService.isRootView) {
              console.log('Setting up infinite view');
              this.cardView = CardType.INFINITE;
              this.items4view = this.temporaryItem;
              this.prepareCardForInfiniteScroll();
              if (forceChangeRoot) {
                this.infiniteContent.scrollToTop(0);
              }
            } else {
              console.log('Setting up paginated view');
              this.items4view = (this.catalogService.isRootView || this.cardView == 'small') ? this.temporaryItem : this.temporaryItem.filter(item => item.type !== 'CATEGORY');
              this.prepareCardByPage();
            }

            if (this.slides) this.slides.swiper.slideTo(0, 0);
          } else {
            console.warn('temporaryItem is empty, setting empty arrays');
            this.items4view = [];
            this.items4Infinite = [];
          }

          // Aggiunge la categoria selezionata alla cronologia
          if (clickedCategory) {
            this._store.dispatch(addSelection({ item: { name: clickedCategory.name, idRootCategory: clicked.id, id: clickedCategory.id.toString(), isProduct: clickedCategory.isProduct } }));

            if (!fromBack) {
              let navStack = [];
              this._store.pipe(select('navStack')).subscribe(res => navStack = res).unsubscribe();
              navStack = navStack.map((roba) => (roba.item as Selection).id);

              this._store.dispatch(addNavHistory({ item: { type: 'CATEGORY', id: clickedCategory.id.toString(), path: navStack, datacolCategory: null } }));
            }

            this.activeRootCategory = clickedCategory;
          }
        }

        if (!fromBack) this.loading.dismiss();
      } catch (error) {
        this.loading.dismiss();
      }
    }
    else if (clicked.type === 'PRODUCT') {
      console.log('Opening product:', clicked.id);
      let filtered = this.items4view.filter(item => item.id === clicked.id);
      console.log('Filtered products:', filtered);
      if (filtered.length > 1) filtered = filtered.slice(0, 1);

      if (!!filtered && filtered.length === 1) {
        const datacolCategory = filtered[0];
        console.log('Found datacolCategory:', datacolCategory);
        if (!datacolCategory) {
          console.error('No datacolCategory found for product');
          Utils.showSnack(this._toastController, this._translate.instant('CATALOG.SELECTED_ERROR'));
          return;
        }
        let navStack = [];
        this._store.pipe(select('navStack')).subscribe(res => navStack = res).unsubscribe();
        navStack = navStack.map((roba) => (roba.item as Selection).id);
        console.log('Current navStack:', navStack);

        // Get the root category ID from the navStack or use the provided idRootCategory
        let rootCategoryId = clicked.idRootCategory;
        if (!rootCategoryId && navStack && navStack.length > 0) {
          // Get the root category from the store
          let categories = [];
          this._store.pipe(select('categories')).subscribe(res => categories = res).unsubscribe();

          const rootCategoryName = navStack[0].replace('id-categoria-', '');
          const rootCategory = categories.find(item => item.name === rootCategoryName);
          rootCategoryId = rootCategory ? rootCategory.id : null;

          console.log('Root category search:', {
            rootCategoryName,
            categories,
            foundCategory: rootCategory,
            rootCategoryId
          });
        }

        // If we still don't have a rootCategoryId, try to get it from the datacolCategory
        if (!rootCategoryId && datacolCategory.idRootCategory) {
          rootCategoryId = datacolCategory.idRootCategory;
          console.log('Using datacolCategory.idRootCategory:', rootCategoryId);
        }

        // Ensure we have a valid rootCategoryId
        if (!rootCategoryId && navStack && navStack.length > 0) {
          rootCategoryId = navStack[0];
          console.log('Using first navStack item as rootCategoryId:', rootCategoryId);
        }

        // Aggiungi la categoria corrente alla cronologia prima di navigare al prodotto
        this._store.dispatch(addNavHistory({ item: { type: 'PRODUCT', id: clicked.id.toString(), path: navStack, datacolCategory: { category: datacolCategory, isFavorite: this.isFavorite(datacolCategory.id.toString()) } } }));

        if (this.catalogService.isInfiniteView) {
          console.log('Handling infinite navigation for product');
          // Cerco in item4Infinite l'item di tipo "CATEGORY" precedente all'item dell'id cliccato
          if (navStack.length > 1) {
            const clickedIndex = this.items4Infinite.findIndex(item => item.id === clicked.id);
            let prevCategory = null;
            if (clickedIndex > 0) {
              for (let i = clickedIndex - 1; i >= 0; i--) {
                if (this.items4Infinite[i].type === 'CATEGORY') {
                  prevCategory = this.items4Infinite[i];
                  break;
                }
              }
            }
            if (!!prevCategory && !!navStack[navStack.length - 1]) {
              const prevCategoryName = navStack[navStack.length - 1].replace('id-categoria-', '');
              if (prevCategory.name !== prevCategoryName) {
                console.log('Updating navStack with previous category:', prevCategory);
                this._store.dispatch(removeLastSelection());
                this._store.dispatch(addSelection({ item: { name: prevCategory.name, idRootCategory: prevCategory.idRootCategory, id: prevCategory.id.toString(), isProduct: false } }));
              }
            }
          }
        }

        const backProduct = { id: clicked.id, idRootCategory: rootCategoryId };
        console.log("Saving backProduct for goBackInfinite:", backProduct);
        localStorage.setItem('backProduct', JSON.stringify(backProduct));
        console.log('Stored backProduct:', backProduct);

        // Log the navigation parameters
        const navigationParams = {
          idRootCategory: rootCategoryId,
          current: JSON.stringify(datacolCategory),
          customerUid: this.catalogService.userUid,
          customerName: this.customerName,
          isFavorite: this.isFavorite(datacolCategory.id.toString()),
          isProspect: this.catalogService.isProspect,
        };

        console.log('Navigation parameters:', navigationParams);
        console.log('datacolCategory object:', datacolCategory);
        console.log('clicked', clicked);
        this._router.navigate([`/private/catalog/products-carousel/${clicked.id}`], {
          queryParams: navigationParams
        }).then(() => {
          console.log('Navigation to product completed');
          if (!fromBack) this.loading.dismiss();
        }).catch(error => {
          console.error('Navigation error:', error);
          if (!fromBack) this.loading.dismiss();
        });
      } else {
        console.error('No product found with id:', clicked.id);
        Utils.showSnack(this._toastController, this._translate.instant('CATALOG.SELECTESTED_ERROR'));
      }
    }

    return new Promise((resolve) => { resolve(null); });
  }


  private setPrevCategory(currentCategory: CatalogCard[], selectedIndex: number) {
    this.prevCategory = null;
    if (currentCategory !== null && selectedIndex > 0) {
      const prev = currentCategory[selectedIndex - 1];
      this.prevCategory = { name: prev.name, id: prev.id, isProduct: prev.isProduct, idRootCategory: prev.idRootCategory };
    }
  }

  private setNextCategory(currentCategory: CatalogCard[], selectedIndex: number) {
    this.nextCategory = null;
    if (currentCategory !== null && currentCategory.length > selectedIndex + 1) {
      const next = currentCategory[selectedIndex + 1];
      this.nextCategory = { name: next.name, id: next.id, isProduct: next.isProduct, idRootCategory: next.idRootCategory };
    }
  }

  /**
   * Recupera una categoria dal database per ID
   * @param categoryId ID della categoria da recuperare
   * @returns Promise<Category | null>
   */
  private async getClickedCategory(categoryId: string): Promise<Category | null> {
    try {
      const category = await this._syncroUnifiedService.getCategoryById(categoryId);
      return category;
    } catch (error) {
      console.error(`❌ Errore nel recupero della categoria ${categoryId}:`, error);
      return null;
    }
  }

  /**
   * Verifica se una categoria è una root category
   * @param category Categoria da verificare
   * @returns boolean
   */
  private isRootCategory(category: Category): boolean {
    if (!category) return false;

    const isRoot = category.isRootCategory;
    // Converte il valore in stringa e verifica
    const rootStr = String(isRoot).toLowerCase();
    return rootStr === 'true' || rootStr === '1';
  }

  /**
   * Naviga verso le categorie figlie di una root category
   * @param rootCategoryId ID della root category
   */
  private navigateToRootCategoryChildren(rootCategoryId: string): void {
    console.log(`🔄 Navigazione verso categorie figlie della root category: ${rootCategoryId}`);

    this._router.navigate(['/private/catalog'], {
      queryParams: {
        rootId: rootCategoryId,
        customerUid: this.catalogService.userUid,
        customerName: this.customerName,
        isProspect: this.catalogService.isProspect
      }
    });
  }

  async goToCategory(event: { idCategory: string | null, idRootCategory: string | null }, turnArourn = false, comeFromDC?) {
    this.catalogService.setRootView(true);
    let categories = [];
    this._store.pipe(select('categories')).subscribe(res => categories = res);
    this.dcProperty.length === 0 && this._store.pipe(select('dcproperty')).subscribe(res => this.dcProperty = [...res]).unsubscribe();
    this.prevCategory = null;
    this.nextCategory = null;
    this.items4view = categories.map((item: Category) => {
      const card = CatalogCard.fromCategory(item, 'CATEGORY', this.isFavorite(item.id.toString()));

      // Imposta l'idRootCategory se fornito nell'evento
      if (event && event.idRootCategory) {
        card.idRootCategory = event.idRootCategory;
      }

      // Aggiorna i campi specifici per la visualizzazione
      card.focus = this.dcProperty.filter(f => f.idSubCategory.toString() === item.id.toString() && f.focus == 'S').length > 0 ? 'S' : 'N';
      card.divisionStatusCode = this.dcProperty.filter(f => f.idSubCategory.toString() === item.id.toString() && f.divisionStatusCode == 'Z3').length > 0 ? 'Z3' : '';

      return card;
    });
    this.items4Infinite = [];
    this.showBottomInfiniteLoader = true;
    this.showTopInfiniteLoader = true;
    var found: boolean = false
    let tmpNavQ = [];
    this.navigationQueue$.subscribe(res => tmpNavQ = [...res]).unsubscribe();
    this._store.dispatch(resetNavStack());
    if (!!event.idCategory) {
      {
        const element = tmpNavQ[0];
        if (!!element && !!element.item) {
          this.openCategory({ id: element.item.id, type: 'CATEGORY', idRootCategory: event.idRootCategory }, turnArourn).then(_ => {
            if (!!comeFromDC) {
              setTimeout(() => {
                const cardIndex = this.temporaryItem.findIndex(item => item.id.toString() === comeFromDC.toString());
                const page = Math.floor(cardIndex / this.cardForPage) + 1;
                this.goToPage(page - 1); // le pagine iniziano da zero
              }, 1000);
            } else {
              this.loading.dismiss();
              this.sliderOpacity = 1;
            }
          })
        }
      }
    } else {
      this.catalogService.setRootView(true);
      this.temporaryItem = [];
      if (!this.showFavorites) {
        this._store.dispatch(resetNavStack());
        this._store.dispatch(resetNavHistory()); // in questo caso parto da zero, va bene resettare tutto
      }
      this.drawGrid(false);
    }
  }

  async goToCategorySiblings(category: Selection) {
    let categories = [];
    this._store.pipe(select('categories')).subscribe(res => categories = res);
    this.dcProperty.length === 0 && this._store.pipe(select('dcproperty')).subscribe(res => this.dcProperty = [...res]).unsubscribe();
    this.items4view = categories.map((item: Category) => {
      const card = CatalogCard.fromCategory(item, 'CATEGORY', this.isFavorite(item.id.toString()));

      // Imposta l'idRootCategory dalla categoria fornita
      card.idRootCategory = category.idRootCategory;

      // Aggiorna i campi specifici per la visualizzazione
      card.focus = this.dcProperty.filter(f => f.idSubCategory.toString() === item.id.toString() && f.focus == 'S').length > 0 ? 'S' : 'N';
      card.divisionStatusCode = this.dcProperty.filter(f => f.idSubCategory.toString() === item.id.toString() && f.divisionStatusCode == 'Z3').length > 0 ? 'Z3' : '';

      return card;
    });
    var found: boolean = false
    let tmpNavQ = [];
    this.navigationQueue$.subscribe(res => tmpNavQ = [...res]).unsubscribe();
    this._store.dispatch(resetNavStack());
    if (!!category) {
      // Devo tornare indietro di un giro e aprire la nuova categoria
      tmpNavQ.pop();
      tmpNavQ.push({ type: null, item: category })
      tmpNavQ.forEach(element => {
        if (!found) {
          found = element.item.id === category.id;
          this.openCategory({ id: element.item.id, type: 'CATEGORY', idRootCategory: category.idRootCategory }, found);
        }
      });

    }
  }

  async goToNextCategory(category: { current: Category, next: Category } | null) {
    if (!!category) {
      if (this.cardView === 'infinite') {
        let navStack = [];
        this._store.pipe(select('navStack')).subscribe(res => navStack = res).unsubscribe();
        navStack = navStack.map((roba) => (roba.item as Selection).id);
        this._store.dispatch(addNavHistory({ item: { type: 'CATEGORY', id: category.current.id.toString(), path: navStack, datacolCategory: null } }));
        this._store.subscribe(res => { console.log('res', res) }).unsubscribe();
        this._store.dispatch(resetNavStack()); // la barra va fatta da zero
        this._store.dispatch(addSelection({ item: { name: category.next.name, id: category.next.id.toString(), isProduct: category.next.isProduct, idRootCategory: category.next.id } }));
        this.openCategory({ id: category.next.id, type: 'CATEGORY', idRootCategory: category.next.id, }, false, true)
      } else {
        let navStack = [];
        this._store.pipe(select('navStack')).subscribe(res => navStack = res).unsubscribe();
        navStack = navStack.map((roba) => (roba.item as Selection).id);
        this._store.dispatch(addNavHistory({ item: { type: 'CATEGORY', id: category.current.id.toString(), path: navStack, datacolCategory: null } }));
        this._store.dispatch(resetNavStack()); // la barra va fatta da zero
        this._store.dispatch(addSelection({ item: { name: category.next.name, id: category.next.id.toString(), isProduct: category.next.isProduct, idRootCategory: category.next.id } }));
        this.openCategory({ id: category.next.id, type: 'CATEGORY', idRootCategory: category.next.id, }, false, true).then(() => this.goToPage(0, true));
      }
    }
  }

  async goComponentGoBack() {
    let navStackSize = 0;
    this.navigationQueue$.subscribe(res => navStackSize = res.length).unsubscribe();

    if (this.showFavorites) {
      let navHistory = [];
      this._store.pipe(select('navHistory')).subscribe(res => navHistory = res).unsubscribe();

      if (navHistory.length > 0) {
        const categoryToOpen = navHistory[navHistory.length - 1].item;

        this._store.dispatch(removeLastRoutingStack());
        if (categoryToOpen.type === 'CATEGORY') {
          this.catalogService.setRootView(true);
          this._router.navigate(['/private/catalog'], {
            queryParams: {
              customerUid: this.catalogService.userUid,
              customerName: this.customerName,
              isProspect: false,
              showFavorites: false,
              clickedNavarId: categoryToOpen.id,
              turnAround: true
            }
          });
        } else {
          this._location.back();
        }
      } else {
        this.catalogService.setRootView(true);
        this._router.navigate(['/private/catalog'], {
          queryParams: {
            customerUid: this.catalogService.userUid,
            customerName: this.customerName,
            isProspect: false,
            showFavorites: false,
            reload: new Date().getTime()
          }
        });
      }
    }
    else if (navStackSize === 0) {
      let routingStack = [];
      this._store.pipe(select('routingStack')).subscribe(res => routingStack = [...res]).unsubscribe();

      let condition = routingStack.length > 0 && routingStack[routingStack.length - 1].component.includes('catalog');
      while (condition) {
        routingStack.pop();
        condition = routingStack.length > 0 && routingStack[routingStack.length - 1].component.includes('catalog');
      }

      if (routingStack.length > 0) {
        this._router.navigate([routingStack[0].component]);
      } else {
        this._router.navigate(['/private/home']);
      }
    } else {

      let navHistory = [];
      this._store.pipe(select('navHistory')).subscribe(res => navHistory = res).unsubscribe();

      if (navHistory.length === 0) {
        this._router.navigate(['/private/home']);
      } else {

        if (navHistory.length === 1) {
          this._store.dispatch(resetNavStack());
          this._store.dispatch(resetNavHistory());
          this.prevCategory = null;
          this.nextCategory = null;
          this.catalogService.setRootView(true);
          this.items4view = [];
          this.items4Infinite = [];
          this.currentPageIndex = 0;
          this.setRowsAndColumns();
          this.getAllCategories();
          this._cdr.markForCheck();
        } else {

          let categories = [];
          this._store.pipe(select('categories')).subscribe(res => categories = res);

          if (this.dcProperty.length === 0) {
            this._store.pipe(select('dcproperty')).subscribe(res => this.dcProperty = [...res]).unsubscribe();
          }

          this.items4view = categories.map((item: Category) => {
            const card = CatalogCard.fromCategory(item, 'CATEGORY', this.isFavorite(item.id.toString()));

            // Imposta l'idRootCategory dal primo elemento del path nella cronologia di navigazione
            card.idRootCategory = navHistory[0].item.path[0];

            // Aggiorna i campi specifici per la visualizzazione
            card.focus = this.dcProperty.filter(f => f.idSubCategory.toString() === item.id.toString() && f.focus === 'S').length > 0 ? 'S' : 'N';
            card.divisionStatusCode = this.dcProperty.filter(f => f.idSubCategory.toString() === item.id.toString() && f.divisionStatusCode === 'Z3').length > 0 ? 'Z3' : '';

            return card;
          });

          const lastNavHistory = navHistory[navHistory.length - 2].item;
          if (lastNavHistory.type === 'CATEGORY') {
            this._store.dispatch(removeLastNavHistory());
            this._store.dispatch(resetNavStack());
            if (this.catalogService.isInfiniteView) {
              /* lastNavHistory.path.forEach(idCategory => {
                //TODO: implement infinite navigation
              this.goBackInfinite([idCategory]);
            }); */
              // TODO: QUIO BISOGNA FARE
              console.log("Triggering goBackInfinite with lastNavHistory:", lastNavHistory);
              this.goBackInfinite(lastNavHistory);

            } else {
              this.catalogService.setRootView(true);
              await this.openCategory({ id: lastNavHistory.path[0], type: 'CATEGORY', idRootCategory: lastNavHistory.path[0] }, true);
              const cardIndex = this.temporaryItem.findIndex(item => item.id.toString() === lastNavHistory.id.toString());
              const page = Math.floor(cardIndex / this.cardForPage) + 1;
              this.goToPage(page - 1);
            }
          } else {
            this._store.dispatch(removeLastNavHistory());
            this._store.dispatch(resetNavStack());
            let subcategories = [...categories];
            if (!!subcategories && subcategories.length > 0) {
              navHistory.forEach(element => {
                const category = subcategories.find(item => item.id.toString() === element.item.id.toString());
                if (category && !JSON.parse(category.isProduct)) {
                  subcategories = Utils.objToJson(category.subcategories);
                  this._store.dispatch(addSelection({ item: { name: category.name, id: category.id.toString(), isProduct: category.isProduct, idRootCategory: category.idRootCategory } }));
                }
              });
            }
            if (this.catalogService.isInfiniteView) {
              console.log("goComponentGoBack: handling PRODUCT in infinite navigation, lastNavHistory=", lastNavHistory);
              if (lastNavHistory && lastNavHistory.path) {
                this.goBackInfinite(lastNavHistory);
              } else {
                console.warn("goComponentGoBack: missing navHistory path data for PRODUCT back");
              }
            } else {
              this._location.back();
            }
          }
        }
      }
    }
  }


  showSubcategoriesPreview(selected: CatalogCard) {
    this.subcategories4Preview = [];
    let filtered = this.items4view.filter(item => item.id.toString() === selected.id.toString());
    if (filtered.length > 1)
      filtered = filtered.slice(0, 1);
    if (!!filtered && filtered.length === 1) {
      if (!!filtered[0].subcategories) {
        const subcatgories = Utils.objToJson(filtered[0].subcategories) as Category[];
        this.subcategories4Preview = subcatgories.map((single: Category) => { return { idParent: selected.id.toString(), name: single.name, id: single.id.toString() } });
        this.isSubcatPreviewShown = true;
      }
    }
  }

  closeSubcategoriesPreview() {
    this.isSubcatPreviewShown = false;
  }

  openSubcategory(item: { idParent: string, name: string, id: string, idRootCategory: string }) {
    this.isSubcatPreviewShown = false;
    this.openCategory({ id: item.idParent, type: "CATEGORY", idRootCategory: item.idRootCategory }, true);
    this.openCategory({ id: item.id, type: "CATEGORY", idRootCategory: item.idRootCategory }, false);
  }

  hidePrices() {
    this.isPricesShown = false;
    this.selDatacolCategoryName = '';
    this.selProducts = [];
  }

  async catalogShowPrices(clicked) {
    const price: Price = clicked;
    this.selDatacolCategoryName = price.name;
    await this._dbService.getRecordsByANDCondition('products', [{ key: 'idSubCategory', value: price.id }]).then((data) => {
      this.selProducts = data as Product[];
      this.isPricesShown = true;
      this._cdr.markForCheck();
    });
  }

  async addOneToCart(item: Product) {
    await this._dbService.getRecordsByANDCondition('carts', [{ key: 'agentCode', value: this._lsService.get("customerAgentCode") }, { key: 'idCustomer', value: this.catalogService.userUid }]).then(async (data: Cart[]) => {
      let productsInCart: { idProduct, quantity }[] = [];
      let cartId = null;
      if (!!data && data.length === 1 && data[0].products.length > 0) {
        productsInCart = Utils.objToJson(data[0].products);
        cartId = data[0].id;
      }

      const found = productsInCart.find((current) => current.idProduct.toString() === item.code.toString())
      if (!found) {
        let cart = { idCustomer: null, idCart: null, currentQuantityInCart: 0 };
        this._store.pipe(select('cart')).subscribe(res => cart = res).unsubscribe();

        productsInCart = [{ idProduct: item.code, quantity: +item.minimumDeliveryQuantity }].concat(productsInCart);

        if (!cartId && !!cart.idCart)
          cartId = cart.idCart;
        await this._dbService.insertOrReplace("carts", [{ key: 'agentCode', value: this._lsService.get("customerAgentCode") }, { key: 'idCustomer', value: this.catalogService.userUid }],
          ['id', 'agentCode', 'idCustomer', 'products'],
          [this._lsService.get("customerAgentCode"), this.catalogService.userUid, JSON.stringify(productsInCart)]).then(async _ => {
            if (!cartId) {
              await this._dbService.getRecordsByANDCondition('carts', [{ key: 'agentCode', value: this._lsService.get("customerAgentCode") }, { key: 'idCustomer', value: this.catalogService.userUid }]).then(async (data: Cart[]) => {
                if (!!data[0] && !!data[0]['id']) {
                  const rows = data[0];
                  cartId = rows.id;
                }
              });
            }
            this._store.dispatch(setCart({ cart: { idCustomer: this.catalogService.userUid, idCart: cartId, currentQuantityInCart: (productsInCart.length) } }));
            this.presentToast(this._translate.instant("PRODUCT.ITEM_ADDED") + item.code.replace(/^0+/, ''));
          }
          );
      } else {
        this.presentToast(this._translate.instant("PRODUCT.ITEM_YET_IN_CART") + item.code.replace(/^0+/, ''));
      }
    });
  }

  private async presentToast(msg: string) {
    Utils.showSnack(this._toastController, msg, this._translate.instant("SETTINGS.DONE"));
  }

  refreshViewAfterChangeFavorites() {
    this._store.pipe(select('favorites')).subscribe(res => this.favorites = [...res].filter((item: Favorite) => item.customerUid === this.catalogService.userUid)).unsubscribe();
    if (this.showFavorites) {
      this.drawGrid(false);
    }
  }

  openSearch() {
    this.isFilterShown = true;
    this._cdr.markForCheck();
  }

  async closeProductSearch(selectedProduct: Product) {
    if (!!selectedProduct) {
      const idSubCategory = selectedProduct.idSubCategory;
      this.isFilterShown = false;
      if (!!idSubCategory) {
        // const loading = await this._loadingController.create({
        //   message: this._translate.instant('GENERICS.WAIT'),
        // });
        this.loading.present();
        this._store.dispatch(resetNavStack());
        const lastCategory: Category = await this.getProductFromHierarchy(idSubCategory).then((lastCategory) => lastCategory);
        let navStack = [];
        this._store.pipe(select('navStack')).subscribe(res => navStack = res).unsubscribe();
        navStack = navStack.map((roba) => (roba.item as Selection).id);
        const isFavorite = this.catalogService.isProspect ? false : this.isFavorite(lastCategory.id.toString());
        const datacolCategory = new CatalogCard(lastCategory.id, navStack[0], lastCategory.name, lastCategory.image, lastCategory.isProduct,
          'CATEGORY', Utils.objToJson(lastCategory.subcategories), isFavorite, lastCategory.description,
          this.dcProperty.filter(f => f.idSubCategory.toString() === lastCategory.id.toString() && f.focus == 'S').length > 0 ? 'S' : 'N',
          null,
          this.dcProperty.filter(f => f.idSubCategory.toString() === lastCategory.id.toString() && f.divisionStatusCode == 'Z3').length > 0 ? 'Z3' : '');
        this._store.dispatch(addNavHistory({ item: { type: 'PRODUCT', id: lastCategory.id.toString(), path: navStack, datacolCategory: { category: datacolCategory, isFavorite: isFavorite } } }));
        this._router.navigate([`/private/catalog/products-carousel/${idSubCategory}`], {
          queryParams: {
            idRootCategory: navStack[0],
            current: JSON.stringify(datacolCategory),
            datacolCategory: JSON.stringify(lastCategory),
            customerUid: this.catalogService.userUid,
            customerName: this.customerName,
            isFavorite: isFavorite,
            isProspect: this.catalogService.isProspect,
          }
        }).finally(() => {
          this.loading.dismiss();
        });
      }
    } else {
      this.isFilterShown = false;
    }
  }

  private async getProductFromHierarchy(id: string): Promise<Category> {
    let rootCategories = [];
    this._store.pipe(select('categories')).subscribe(res => rootCategories = res).unsubscribe();
    this.dcProperty.length === 0 && this._store.pipe(select('dcproperty')).subscribe(res => this.dcProperty = [...res]).unsubscribe();
    const category = this.findProductInCategory(rootCategories, id, null);
    this._store.dispatch(reverseSelection());
    return category;
  }

  private findProductInCategory(categories: Category[], idSubCategory: string, idRootCategory: string): Category | null {
    if (categories && categories.length > 0) {
      const list = categories.filter((category: Category) => category.id.toString() === idSubCategory);
      if (!!list && list.length > 0) {
        if (list[0].isProduct)
          this._store.dispatch(addSelection({ item: { name: list[0].name, idRootCategory: (!!idRootCategory ? idRootCategory : null), id: list[0].id.toString(), isProduct: list[0].isProduct } }));
        return list[0];
      }
      else {
        let found = null;
        categories.forEach(async (category: Category) => {
          if (!!found)
            return;
          else {
            const subcategory = !!category.subcategories ? Utils.objToJson(category.subcategories) : [];
            if (subcategory.length > 0) {
              found = this.findProductInCategory(subcategory, idSubCategory, idRootCategory);
              if (found && !category.isProduct)
                this._store.dispatch(addSelection({ item: { name: category.name, idRootCategory: idRootCategory, id: category.id.toString(), isProduct: category.isProduct } }));
            }
            else
              found = null;
          }
        });
        return found;
      }
    } else {
      return null;
    }
  }

  async navToPage(event) {
    this.loading = await this._loadingController.create({
      message: this._translate.instant('GENERICS.WAIT'),
    });
    this.loading.present();

    this.sliderOpacity = 0;
    let categories = [];
    this._store.pipe(select('categories')).subscribe(res => categories = res);
    this.dcProperty.length === 0 && this._store.pipe(select('dcproperty')).subscribe(res => this.dcProperty = [...res]).unsubscribe();
    {
      let navHistory = [];
      this._store.pipe(select('navHistory')).subscribe(res => navHistory = [...res]).unsubscribe();
      const rootCategory = event.queue[0].toString();
      const targetCategory = event.queue[event.queue.length - 1].toString();

      // Ricarico le root
      this.items4view = categories.map((item: Category) => {
        const card = CatalogCard.fromCategory(item, 'CATEGORY', this.isFavorite(item.id.toString()));

        // Aggiorna i campi specifici per la visualizzazione
        card.focus = this.dcProperty.filter(f => f.idSubCategory.toString() === item.id.toString() && f.focus == 'S').length > 0 ? 'S' : 'N';
        card.divisionStatusCode = this.dcProperty.filter(f => f.idSubCategory.toString() === item.id.toString() && f.divisionStatusCode == 'Z3').length > 0 ? 'Z3' : '';

        return card;
      });

      // Trovo la categoria target
      const category = this.temporaryItem.find(item => item.id.toString() === targetCategory);

      if (!!category) {
        this._store.dispatch(addSelection({ item: { name: category.name, idRootCategory: rootCategory, id: category.id.toString(), isProduct: false } }));
        this._store.dispatch(addNavHistory({ item: { type: 'CATEGORY', id: category.id.toString(), path: event.queue.slice(0, event.queue.length), datacolCategory: null } }));
      }

      this.catalogService.setRootView(true);
      this._store.dispatch(resetNavStack());

      // Apriamo la categoria root
      await this.openCategory({ id: rootCategory, type: 'CATEGORY', idRootCategory: null }, true);

      // Gestione diversa per le visualizzazioni 'P' e 'I'
      if (this.cardView === 'infinite') {
        // Per la visualizzazione infinita
        if (event.queue.length === 1) {
          // Se stiamo cliccando direttamente sulla root, scrolliamo in alto
          this.infiniteContent.scrollToTop(0);
          this.items4Infinite = [];
          this.creaArrayInfinito(this.items4view, this.cardForPage, 'init');
        } else {
          // Altrimenti usiamo il metodo calcPageToOpen
          const page = this.calcPageToOpen(event);
          if (page !== null) {
            this._cdr.detectChanges();
            this._cdr.markForCheck();

            setTimeout(() => {
              // Aggiorniamo la selezione e la cronologia di navigazione
              const updatedCategory = this.temporaryItem.find(item => item.id.toString() === targetCategory);
              if (!!updatedCategory) {
                this._store.dispatch(addSelection({ item: { name: updatedCategory.name, idRootCategory: rootCategory, id: updatedCategory.id.toString(), isProduct: false } }));
                this._store.dispatch(addNavHistory({ item: { type: 'CATEGORY', id: updatedCategory.id.toString(), path: event.queue.slice(0, event.queue.length), datacolCategory: null } }));
              }

              this._cdr.detectChanges();
              this._cdr.markForCheck();

              this.loading.dismiss();
              this.sliderOpacity = 1;
            }, 1000);
          } else {
            this.loading.dismiss();
            this.sliderOpacity = 1;
          }
        }
      } else {
        // Per la visualizzazione paginata
        this._cdr.detectChanges();
        this._cdr.markForCheck();

        setTimeout(() => {
          if (event.queue.length === 1) {
            // Se stiamo cliccando direttamente sulla root, andiamo alla prima pagina
            this.goToPage(0, false);
          } else {
            // Altrimenti calcoliamo la pagina corretta
            const targetIndex = this.temporaryItem.findIndex(item => item.id.toString() === targetCategory);
            if (targetIndex !== -1) {
              const targetPage = Math.floor(targetIndex / this.cardForPage);
              this.goToPage(targetPage, false);
            }
          }

          // Aggiorniamo la selezione e la cronologia di navigazione
          const updatedCategory = this.temporaryItem.find(item => item.id.toString() === targetCategory);
          if (!!updatedCategory) {
            this._store.dispatch(addSelection({ item: { name: updatedCategory.name, idRootCategory: rootCategory, id: updatedCategory.id.toString(), isProduct: false } }));
            this._store.dispatch(addNavHistory({ item: { type: 'CATEGORY', id: updatedCategory.id.toString(), path: event.queue.slice(0, event.queue.length), datacolCategory: null } }));
          }

          this._cdr.detectChanges();
          this._cdr.markForCheck();

          this.prepareCardByPage();

          this.loading.dismiss();
          this.sliderOpacity = 1;
        }, 1000);
      }
    }
  }

  public beforeGoToExtras() {
    if (this.itemsByPages[this.currentPageIndex].length > 0) {
      const currentCategory = this.itemsByPages[this.currentPageIndex][0];
      let navStack = [];
      this._store.pipe(select('navStack')).subscribe(res => navStack = res).unsubscribe();
      navStack = navStack.map((roba) => (roba.item as Selection).id);
      this._store.dispatch(addNavHistory({ item: { type: 'CATEGORY', id: currentCategory.idRootCategory.toString(), path: navStack, datacolCategory: { category: currentCategory, isFavorite: this.isFavorite(currentCategory.id.toString()) } } }));
    }
  }

  onShowNavigator(event) {
    const showNavigator: boolean = event;
    const panelComponent = document.getElementById('panelComponent');
    if (panelComponent && showNavigator) {
      (panelComponent as any).onOpen();
    }
    this.showNavigator = showNavigator;
  }

  switchCardView(event) {
    localStorage.setItem('cardViewType', event.cardView);
    this.cardView = event.cardView;
    this.setRowsAndColumns();
    this.items4view = (this.catalogService.isRootView || this.cardView == 'small') ? this.temporaryItem : this.temporaryItem.filter(item => item.type !== 'CATEGORY');
    this.goToPage(0);
    this.prepareCardByPage();
    this.drawGrid(true);
  }

  openTagFilterDialog() {
    console.log('Opening tag filter dialog');

    // Get current category information
    const currentCategory = this.catalogService.currentCategoryData;
    if (!currentCategory) {
      console.warn('No current category available for tag filtering');
      return;
    }

    // Get catalog ID from localStorage or other source
    const catalogId = 1; // TODO: Get this from the actual catalog configuration

    // Get selected subcategories IDs
    const selectedSubcategoryIds = [parseInt(currentCategory.idSubCategory || currentCategory.id)];

    const dialogRef = this._dialog.open(AssignTagDialogComponent, {
      width: '1400px',
      maxWidth: 'none',
      data: {
        action: 'MANAGE',
        idSubCategory: selectedSubcategoryIds,
        idCatalog: catalogId,
        categoryName: currentCategory.name
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        console.log('Tag filter dialog closed with result:', result);
        // TODO: Apply filters based on selected tags
      }
    });
  }

  rootToReload: Category = null;

  private findNodeById(categories: Category[], id: string): Category | null {

    for (let category of categories) {
      if (category.id.toString() === id.toString()) {
        this.rootToReload = category;
        return category;
      }
      if (!category.isProduct && !!category.subcategories) {
        const found = this.findNodeById(Utils.objToJson(category.subcategories), id);
        if (found) {
          this.rootToReload = category;
          return found;
        }
      }
    }
    return null;
  }

  private findFirstProductNode(node: Category): Category | null {
    if (node.isProduct) {
      return node;
    }
    for (const subcategory of Utils.objToJson(node.subcategories)) {
      const foundProduct = this.findFirstProductNode(subcategory as unknown as Category);
      if (foundProduct) {
        return foundProduct;
      }
    }
    return null;
  }



  cardWidth: number = 200;
  cardsNumber: number = 4;

  calculateCardPerRow() {
    if (this.catalogService.isRootView) {
      const height = this.screenHeight - (220 /* window.devicePixelRatio*/); // tolgo header, footer, paginator, padding vari
      const width = this.screenWidth - (50 /* window.devicePixelRatio*/);
      this.cardDimension.columns = Math.floor(width / (this.CARD_WIDTH + this.CARD_GAP));
      this.cardDimension.rows = Math.floor(height / this.CARD_HEIGHT);
      this.cardForPage = this.cardDimension.rows * this.cardDimension.columns;
    } else {
      const isPortrait = this.screenWidth <= this.screenHeight;
      this.cardsNumber = isPortrait ? 12 / 3 : 12 / 4;
      this.cardDimension.columns = isPortrait ? 3 : 4;
    }
    this._cdr.detectChanges();
  }

  showTopInfiniteLoader: boolean = true;
  showBottomInfiniteLoader: boolean = true;

  checkCategory(element, previousElement) {
    /* if (element.type === 'CATEGORY' && element.level == "1") {
      return true;
    }
    return false; */
    if (element.type === 'PRODUCT') {
      return true;
    } else if ((element.level && element.type == 'CATEGORY' && previousElement == null) || (!!previousElement && previousElement.type === 'PRODUCT' && element.type === 'CATEGORY')) {
      return true;
    }
    return false;
  }

  goToNextCategoryInfinite(nextCategory: Category) {
    this.showNextCategoryButton = false;

    //TODO: volendo possiamo aggiungere un salvataggio della subcategory corrente per tornare indietro e controllare poi nella funzione creaArrayInfinito se ho valorizzata la subcategory precedente
    /* this.items4Infinite = []; */
    this.navToPage({ queue: [nextCategory.id] });
  }


  async downInfiniteScroll() {
    if (!this.ionContent) return;
    await this.creaArrayInfinito(this.items4view, this.cardForPage, 'down');
  }

  async onLoadMoreItems(direction: 'up' | 'down') {
    console.log('onLoadMoreItems called with direction:', direction);
    await this.creaArrayInfinito(this.items4view, this.cardForPage, direction);
  }

  async creaArrayInfinito(arrayOriginale: CatalogCard[], elementiPerCaricamento: number, direzione: 'init' | 'up' | 'down') {

    if (!Array.isArray(arrayOriginale) || elementiPerCaricamento <= 0) return;

    // Se è il primo caricamento
    if (direzione === 'init' || this.items4Infinite.length === 0) {
      await this.primoCaricamento(arrayOriginale, elementiPerCaricamento);
    }
    // Se aggiungiamo elementi
    else if (!this.infiniteDisabled) {
      let nuoviElementi: CatalogCard[] = [...this.items4Infinite];

      if (direzione === 'down') {
        this.caricaElementiInFondo(nuoviElementi, arrayOriginale, elementiPerCaricamento);
      } else if (direzione === 'up') {
        this.caricaElementiInTesta(nuoviElementi, arrayOriginale, elementiPerCaricamento);
      }

      this.items4Infinite = [...nuoviElementi];
    }

    this.sliderOpacity = 1;
  }

  // Gestisce il primo caricamento della lista
  async primoCaricamento(arrayOriginale: CatalogCard[], elementiPerCaricamento: number) {
    console.log('primoCaricamento called', {
      arrayOriginaleLength: arrayOriginale?.length || 0,
      elementiPerCaricamento,
      isRootView: this.catalogService.isRootView,
      isInfiniteView: this.catalogService.isInfiniteView
    });

    this.loading.present();
    this.infiniteDisabled = true;

    let backProduct = JSON.parse(localStorage.getItem('backProduct'));
    const productIndex = backProduct ? arrayOriginale.findIndex(item => item.id === backProduct.id) : 0;
    localStorage.removeItem('backProduct');

    const startIndex = Math.max(0, productIndex - Math.floor(elementiPerCaricamento / 2));
    this.showTopInfiniteLoader = startIndex > elementiPerCaricamento;

    let categoriaCorrente: string | null = null;
    let newItems: CatalogCard[] = [];

    for (let i = startIndex; i < arrayOriginale.length && newItems.length < elementiPerCaricamento; i++) {
      let previousElement = i > 0 ? arrayOriginale[i - 1] : null;
      let elemento = arrayOriginale[i];

      if (elemento.type === 'CATEGORY' && categoriaCorrente == null) {
        categoriaCorrente = elemento.name;
      }

      if (!this.items4Infinite.includes(elemento) && (elemento.type === 'PRODUCT' || this.checkCategory(elemento, previousElement))) {
        newItems.push(elemento);
      }
    }
    console.log("primoCaricamento completed", {
      newItemsLength: newItems.length,
      items4InfiniteLength: this.items4Infinite.length,
      newItems: newItems.map(item => ({ id: item.id, name: item.name, type: item.type }))
    });

    //this.items4Infinite = []
    this.items4Infinite = [...newItems];
    this.showBottomInfiniteLoader = arrayOriginale.length > elementiPerCaricamento;
    this.showNextCategoryButton = arrayOriginale.length <= elementiPerCaricamento;
    this._cdr.markForCheck();
    if (backProduct) {
      setTimeout(() => {
        const itemElement = document.querySelector(`[data-id='${backProduct.id}']`);
        if (itemElement) {
          itemElement.scrollIntoView({ behavior: 'auto', block: 'center' });
        }
        setTimeout(() => {
          this.infiniteDisabled = false;
          this.loading.dismiss();
        }, 300);
      }, 200);
    } else {
      this.infiniteDisabled = false;
      this.loading.dismiss();
    }
  }

  // Carica nuovi elementi in fondo alla lista
  caricaElementiInFondo(lista: CatalogCard[], arrayOriginale: CatalogCard[], elementiPerCaricamento: number) {
    let ultimoElemento = lista[lista.length - 1];
    let startIndex = arrayOriginale.indexOf(ultimoElemento) + 1;

    for (let i = startIndex; i < startIndex + elementiPerCaricamento && i < arrayOriginale.length; i++) {
      let elemento = arrayOriginale[i];
      let previousElement = i > 0 ? arrayOriginale[i - 1] : null;

      if (!lista.includes(elemento) && (elemento.type === 'PRODUCT' || this.checkCategory(elemento, previousElement))) {
        lista.push(elemento);
      }
    }
    // Confrontiamo gli oggetti interi invece degli ID perché potrebbero esserci elementi diversi
    // con lo stesso ID in categorie diverse. Questo garantisce che stiamo effettivamente
    // guardando lo stesso elemento nell'array originale.
    if (lista[lista.length - 1] === arrayOriginale[arrayOriginale.length - 1]) {
      this.showNextCategoryButton = true;
      this.showBottomInfiniteLoader = false;
    }
  }

  // Carica nuovi elementi in testa alla lista mantenendo la posizione dello scroll
  caricaElementiInTesta(lista: CatalogCard[], arrayOriginale: CatalogCard[], elementiPerCaricamento: number) {
    let primoElemento = lista[0];
    let startIndex = arrayOriginale.indexOf(primoElemento) - 1;

    if (startIndex < 0) {
      this.showTopInfiniteLoader = false;
      return;
    }

    const oldHeight = this.infiniteGrid.nativeElement.scrollHeight;

    for (let i = startIndex; i >= 0 && startIndex - i < elementiPerCaricamento; i--) {
      let elemento = arrayOriginale[i];
      let nextElement = (i + 1) < arrayOriginale.length ? arrayOriginale[i + 1] : null;

      if (!lista.includes(elemento) && (elemento.type === 'PRODUCT' || this.checkCategory(elemento, nextElement))) {
        lista.unshift(elemento);
      }
    }

    // Manteniamo la posizione dello scroll per evitare salti
    requestAnimationFrame(() => {
      const newHeight = this.infiniteGrid.nativeElement.scrollHeight;
      const heightDiff = newHeight - oldHeight;
      this.ionContent.scrollByPoint(0, heightDiff, 0);
    });
  }


  protected showNextCategoryButton: boolean = false;
  private infiniteDisabled: boolean = true;

  async goBackInfinite(category: any) {

    this.loading = await this._loadingController.create({
      message: this._translate.instant('GENERICS.WAIT'),
    });
    this.loading.present();

    this.items4Infinite = [];

    let catID = null;

    if (category.path.length > 1) {
      catID = category.path[category.path.length - 1];
    } else {
      catID = category.path[0];
    }

    const categoryToGoTo = catID;
    let categories = [];

    // Verifica se lo store viene popolato correttamente
    this._store.pipe(select('categories')).subscribe(res => {
      categories = [...res];
    }).unsubscribe();

    const startNode = this.findNodeById(categories, categoryToGoTo);

    this.updateNavbar(this.rootToReload.name);
    let rootCategoryItem = this.flattenCatalog(Utils.objToJson((this.rootToReload as any).subcategories));
    this.items4view = rootCategoryItem;
    console.log("goBackInfinite: items4view ready, forcing reload...");

    if (!startNode) {
      return null;
    }

    const firstDC = this.findFirstProductNode(startNode);

    const index = this.items4view.findIndex(item => item.id.toString() === firstDC.id.toString());
    if (index !== -1) {
      let startIndex = Math.max(0, index - Math.floor(this.cardForPage / 2));
      let endIndex = index + this.cardForPage;

      if (index < this.cardForPage && index > 0) {
        this.showTopInfiniteLoader = false;
      } else {
        this.showTopInfiniteLoader = true;
      }

      if (index > this.items4view.length - this.cardForPage) {
        this.showBottomInfiniteLoader = false;
        this.showNextCategoryButton = true; // Mostra il pulsante quando siamo alla fine
      } else {
        this.showBottomInfiniteLoader = true;
        this.showNextCategoryButton = false;
      }
      this.items4Infinite = []
      for (let i = startIndex; i < this.items4view.length && i - startIndex < this.cardForPage; i++) {
        let elemento = this.items4view[i];
        let nextElement: CatalogCard = (i + 1) < this.items4view.length ? this.items4view[i + 1] : null;
        if (this.checkCategory(elemento, nextElement))
          this.items4Infinite.push(this.items4view[i]);
      }
      let category = this.items4view[index - 1];

      setTimeout(() => {
        const itemElement = document.querySelector(`[data-id='${category.id}']`);

        if (itemElement) {
          itemElement.scrollIntoView({
            behavior: 'auto',
            block: 'start'
          });
        }

        this.infiniteDisabled = false;
        this.loading.dismiss();
      }, 200);
    }
    console.log("goBackInfinite completed. items4Infinite length:", this.items4Infinite.length);
    this.sliderOpacity = 1;
  }

  flattenCatalog(cards: any[]): any[] {

    let result: CatalogCard[] = [];

    for (const card of cards) {
      // Crea l'oggetto CatalogCard usando il nuovo metodo fromCategory se possibile
      let catalogCard: CatalogCard;

      if (card.id && card.name) {
        // Usa il metodo fromCategory per creare la card
        catalogCard = CatalogCard.fromCategory(card, card.isProduct ? 'PRODUCT' : 'CATEGORY', this.isFavorite(card.id.toString()));

        // Aggiorna i campi specifici per la visualizzazione
        catalogCard.focus = this.dcProperty.filter(f => f.idSubCategory.toString() === card.id.toString() && f.focus == 'S').length > 0 ? 'S' : 'N';
        catalogCard.divisionStatusCode = this.dcProperty.filter(f => f.idSubCategory.toString() === card.id.toString() && f.divisionStatusCode == 'Z3').length > 0 ? 'Z3' : '';
      } else {
        // Fallback al costruttore tradizionale per oggetti non standard
        catalogCard = new CatalogCard(
          card.id,
          card.idRootCategory,
          card.name,
          card.image,
          card.isProduct,
          card.isProduct ? 'PRODUCT' : 'CATEGORY',
          Utils.objToJson(card.subcategories),
          this.isFavorite(card.id.toString()),
          card.description,
          this.dcProperty.filter(f => f.idSubCategory.toString() === card.id.toString() && f.focus == 'S').length > 0 ? 'S' : 'N',
          null,
          this.dcProperty.filter(f => f.idSubCategory.toString() === card.id.toString() && f.divisionStatusCode == 'Z3').length > 0 ? 'Z3' : '',
          card.level
        );
      }

      result.push(catalogCard);

      // Se ci sono sotto-categorie, ripeti ricorsivamente
      if (catalogCard.subcategories && catalogCard.subcategories.length > 0) {
        result = result.concat(this.flattenCatalog(Utils.objToJson(catalogCard.subcategories)));
      }

      // Aggiungi eventuali prodotti
      if (catalogCard.products && catalogCard.products.length > 0) {
        result = result.concat(catalogCard.products);
      }
    }
    return result;
  }


  private calcPageToOpen(event) {
    if (this.cardView === 'infinite') {
      this.infiniteDisabled = true;
      this.showBottomInfiniteLoader = true;
      this.showTopInfiniteLoader = true;

      const categoryToGoTo = event.queue[event.queue.length - 1].toString();

      let categories = [];
      this._store.pipe(select('categories')).subscribe(res => {
        categories = [...res];
      }).unsubscribe();

      // Troviamo il nodo della categoria
      const startNode = this.findNodeById(categories, categoryToGoTo);

      if (!startNode) {
        console.warn("goBackInfinite: startNode not found for", categoryToGoTo);
        return null;
      }

      // Invece di cercare il primo prodotto, cerchiamo direttamente la categoria
      const index = this.items4view.findIndex(item =>
        item.type === 'CATEGORY' &&
        item.id.toString() === categoryToGoTo
      );

      if (index !== -1) {
        let startIndex = Math.max(0, index - Math.floor(this.cardForPage / 2));

        if (index < this.cardForPage && index > 0) {
          this.showTopInfiniteLoader = false;
        } else {
          this.showTopInfiniteLoader = true;
        }

        if (index > this.items4view.length - this.cardForPage) {
          this.showBottomInfiniteLoader = false;
          this.showNextCategoryButton = true; // Mostra il pulsante quando siamo alla fine
        } else {
          this.showBottomInfiniteLoader = true;
          this.showNextCategoryButton = false;
        }

        this.items4Infinite = [];
        for (let i = startIndex; i < this.items4view.length && i - startIndex < this.cardForPage; i++) {
          let elemento = this.items4view[i];
          let nextElement: CatalogCard = (i + 1) < this.items4view.length ? this.items4view[i + 1] : null;
          if (this.checkCategory(elemento, nextElement)) {
            this.items4Infinite.push(this.items4view[i]);
          }
        }

        // Verifica se siamo alla fine della lista
        if (this.items4Infinite.length > 0 &&
          this.items4Infinite[this.items4Infinite.length - 1] === this.items4view[this.items4view.length - 1]) {
          this.showNextCategoryButton = true;
          this.showBottomInfiniteLoader = false;
        }

        // Utilizziamo la categoria invece del prodotto per lo scroll
        const category = this.items4view[index];

        setTimeout(() => {
          // Cerchiamo l'elemento usando l'ID della categoria
          const itemElement = document.querySelector(`[data-id='${categoryToGoTo}']`);

          if (itemElement) {
            itemElement.scrollIntoView({
              behavior: 'auto',
              block: 'start'
            });
          }

          this.infiniteDisabled = false;
        }, 200);

        // Aggiorniamo lo store con la categoria corretta
        this._store.dispatch(addSelection({
          item: {
            name: category.name,
            id: category.id.toString(),
            isProduct: false,
            idRootCategory: event.queue[0]
          }
        }));
        this._store.dispatch(addNavHistory({
          item: {
            type: 'CATEGORY',
            id: category.id.toString(),
            path: event.queue.slice(0, event.queue.length),
            datacolCategory: null
          }
        }));
      }

      return index;
    } else {
      // ... resto del codice per altre visualizzazioni ...
    }
  }

  longPressEnabled: boolean = false;





  private updateNavbar(categoria: string) {
    if (!!categoria && !this.catalogService.isRootView) {
      let navStack = [];
      this.navigationQueue$.subscribe(res => navStack = [...res]).unsubscribe();
      if (navStack.length > 1) {
        this._store.dispatch(removeLastSelection());
      }

      if (this.catalogService.isInfiniteView) { // c'è sempre al massimo una sola categoria da mostrare in questo caso
        let navStack = [];
        this.navigationQueue$.subscribe(res => navStack = [...res]).unsubscribe();
        if (navStack.length > 1)
          this._store.dispatch(removeLastSelection());
        this._store.dispatch(addSelection({ item: { name: categoria, id: 'id-categoria-' + categoria, isProduct: false, idRootCategory: 'root-category-id' } }));
      }
      else
        this._store.dispatch(addSelection({ item: { name: categoria, id: 'id-categoria-' + categoria, isProduct: false, idRootCategory: 'root-category-id' } }));
    }
  }


  @ViewChild('infiniteContent') private infiniteContent: IonContent;






  // REFACTORING DEVELOPMENT


}
