import { Component, OnInit, CUSTOM_ELEMENTS_SCHEMA, inject, ViewChild, ElementRef, ChangeDetectorRef, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule, IonicSlides } from '@ionic/angular';
import { LargeCatalogCardComponent } from "../catalog-card/large-catalog-card/large-catalog-card.component";
import { MediumCatalogCardComponent } from "../catalog-card/medium-catalog-card/medium-catalog-card.component";
import { SmallCatalogCardComponent } from '../catalog-card/small-catalog-card/small-catalog-card.component';
import { CardType, CatalogService } from 'src/app/service/catalog/catalog.service';
import { CatalogCard } from 'src/app/service/data/catalog-card';
import { NavigationService } from 'src/app/service/navigation/navigation.service';
import { ActivatedRoute, Router } from '@angular/router';
import { SyncroUnifiedService } from 'src/app/service/syncro-unified/syncro-unified.service';
import { select, Store } from '@ngrx/store';
import { Category } from 'src/app/service/data/category';
import Utils from 'src/app/shared/utils';
import { Favorite } from 'src/app/service/data/favorite';
import { setRootCategories } from 'src/app/store/actions/root-categories.actions';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { TagFilterService } from '../../services/tag-filter.service';
import { CategoryService } from 'src/app/service/category/category.service';

@Component({
  selector: 'app-paginated-catalog',
  templateUrl: './paginated-catalog.component.html',
  styleUrls: ['./paginated-catalog.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  standalone: true,
  imports: [
    CommonModule,
    IonicModule,
    LargeCatalogCardComponent,
    MediumCatalogCardComponent,
    SmallCatalogCardComponent
  ],
})
export class PaginatedCatalogComponent implements OnInit, OnDestroy {

  // Items della pagina corrente - sempre 12 card per pagina
  items: CatalogCard[] = [];

  @ViewChild('swiperContainer', { static: false }) swiperContainer!: ElementRef;

  // Services injection
  protected catalogService = inject(CatalogService);
  protected navigationService = inject(NavigationService);
  private _activatedRoute = inject(ActivatedRoute);
  private _syncroUnifiedService = inject(SyncroUnifiedService);
  private _store = inject(Store);
  private _cd = inject(ChangeDetectorRef);
  private _router = inject(Router);
  private _tagFilterService = inject(TagFilterService);
  private _categoryService = inject(CategoryService);

  // Destroy subject per unsubscribe
  private destroy$ = new Subject<void>();

  // Layout configuration - FISSO a 12 card per pagina
  protected itemsPerPage = 12;
  protected columns = 3;
  protected rows = 4;
  protected slidesPerGroup = 1; // Una pagina alla volta
  protected swiperModules = [IonicSlides];

  // Data management
  protected favorites: any[] = [];
  protected allCategories: Category[] = [];

  // Pagination state
  protected currentPage = 1;
  protected totalItems = 0;
  protected totalPages = 0;

  // Loading states
  protected isLoading = false;
  protected isNavigating = false;

  // Navigation state
  protected rootId: string | null = null;
  protected categoryId: string | null = null;

  // Cache per le pagine già caricate (mantieni max 10 pagine in memoria)
  private pageCache = new Map<number, CatalogCard[]>();
  private readonly MAX_CACHED_PAGES = 10;
  activeCardView: CardType;

  constructor() { }

  getEmptySlotsCount(pageIndex: number): number {
    return Math.max(0, (this.columns * this.rows) - this.getPageItems(pageIndex).length);
  }

  ngOnInit() {
    this.setupCardViewSubscription();
    this.dispatch();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private setupCardViewSubscription() {
    this.catalogService.cardView.pipe(
      takeUntil(this.destroy$)
    ).subscribe((cardView) => {
      console.log('Card view changed to:', cardView);
      this.activeCardView = cardView;

      // Aggiorna layout ma mantieni sempre 12 card per pagina
      switch (cardView) {
        case CardType.SMALL:
          this.columns = 3;
          this.rows = 4;
          this.itemsPerPage = 12; // Sempre 12 card per pagina
          break;
        case CardType.MEDIUM:
          this.columns = 2;
          this.rows = 2;
          this.itemsPerPage = 4; // Sempre 4 card per pagina
          break;
        case CardType.LARGE:
          this.columns = 2;
          this.rows = 1;
          this.itemsPerPage = 2; // Sempre 2 card per pagina
          break;
        default:
          this.columns = 3;
          this.rows = 4;
          this.itemsPerPage = 12; // Default a 12 card per pagina
          break;
      }

      // Ricarica la pagina corrente con il nuovo layout
      if (this.allCategories.length > 0) {
        this.pageCache.clear();
        this.loadPageItems(this.currentPage);
      }
    });
  }

  private recalculatePagination() {
    this.totalPages = Math.ceil(this.totalItems / this.itemsPerPage);
    console.log(`📊 Total: ${this.totalItems} items, ${this.totalPages} pages`);
  }

  // Getter per le pagine visibili nel paginatore
  getVisiblePages(): number[] {
    const maxVisible = 8;
    const halfVisible = Math.floor(maxVisible / 2);

    let startPage = Math.max(1, this.currentPage - halfVisible);
    let endPage = Math.min(this.totalPages, startPage + maxVisible - 1);

    if (endPage - startPage < maxVisible - 1) {
      startPage = Math.max(1, endPage - maxVisible + 1);
    }

    const pages = [];
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    return pages;
  }

  // Carica una pagina specifica e gestisce il cache
  private loadPageItems(page: number) {
    if (page < 1 || page > this.totalPages) {
      console.warn(`⚠️ Invalid page: ${page}`);
      return;
    }

    console.log(`📄 Loading page ${page}/${this.totalPages}`);

    // Precarica le pagine nell'intorno PRIMA di cambiare pagina
    this.preloadAdjacentPages(page);

    // Mostra la pagina corrente
    this.items = this.pageCache.get(page) || [];
    this.currentPage = page;

    // Aggiorna swiper per mostrare la pagina corretta
    this.updateSwiperToPage(page);

    this._cd.detectChanges();
  }

  // Carica una singola pagina in cache se non esiste
  private loadSinglePageToCache(page: number): CatalogCard[] {
    if (page < 1 || page > this.totalPages) {
      return [];
    }

    if (!this.pageCache.has(page)) {
      const startIndex = (page - 1) * this.itemsPerPage;
      const endIndex = Math.min(startIndex + this.itemsPerPage, this.allCategories.length);
      const pageCategories = this.allCategories.slice(startIndex, endIndex);

      const pageItems = pageCategories.map((item: Category) => {
        const card = CatalogCard.fromCategory(
          item,
          Utils.objToJson(item.isProduct) ? 'PRODUCT' : 'CATEGORY',
          this.isFavorite(item.id.toString())
        );
        return card;
      });

      this.pageCache.set(page, pageItems);
      console.log(`✅ Page ${page} cached with ${pageItems.length} items`);
    }

    return this.pageCache.get(page) || [];
  }

  // Pulisce la cache mantenendo solo le pagine più recenti
  private cleanupCache() {
    if (this.pageCache.size > this.MAX_CACHED_PAGES) {
      // Ordina per distanza dalla pagina corrente (mantieni quelle più vicine)
      const pages = Array.from(this.pageCache.keys()).sort((a, b) =>
        Math.abs(a - this.currentPage) - Math.abs(b - this.currentPage)
      );

      const pagesToRemove = pages.slice(this.MAX_CACHED_PAGES);
      pagesToRemove.forEach(page => {
        this.pageCache.delete(page);
        console.log(`🗑️ Removed page ${page} from cache`);
      });
    }
  }

  // Precarica pagine adiacenti (±3 pagine)
  private preloadAdjacentPages(currentPage: number) {
    const range = 3; // Precarica ±3 pagine
    const pagesToPreload = [];

    for (let i = -range; i <= range; i++) {
      const page = currentPage + i;
      if (page >= 1 && page <= this.totalPages && !this.pageCache.has(page)) {
        pagesToPreload.push(page);
      }
    }

    pagesToPreload.forEach(page => {
      this.loadSinglePageToCache(page);
    });

    this.cleanupCache();
  }

  // Ottieni i dati di una pagina specifica (per il template)
  getPageItems(pageIndex: number): CatalogCard[] {
    const page = pageIndex + 1;

    // Se la pagina non è in cache, caricala
    if (!this.pageCache.has(page)) {
      console.log(`🔄 Page ${page} not in cache, loading...`);
      return this.loadSinglePageToCache(page);
    }

    return this.pageCache.get(page) || [];
  }

  // Verifica se una pagina è caricata
  isPageLoaded(pageIndex: number): boolean {
    const page = pageIndex + 1;
    return this.pageCache.has(page);
  }

  // Aggiorna swiper per mostrare la pagina corretta
  private updateSwiperToPage(page: number) {
    if (this.swiperContainer?.nativeElement?.swiper) {
      const swiperInstance = this.swiperContainer.nativeElement.swiper;
      const slideIndex = (page - 1);

      // Evita loop infiniti
      if (swiperInstance.realIndex !== slideIndex) {
        swiperInstance.slideTo(slideIndex, 300);
      }
    }
  }

  // Navigazione diretta a una pagina tramite il paginatore
  goToPage(pageNumber: number) {
    if (pageNumber < 1 || pageNumber > this.totalPages || pageNumber === this.currentPage || this.isNavigating) {
      return;
    }

    console.log(`🎯 Navigating to page ${pageNumber}`);

    this.isNavigating = true;

    // Aggiorna l'URL
    this.updateUrlWithPage(pageNumber);

    // Carica la nuova pagina
    this.loadPageItems(pageNumber);

    // Reset del flag dopo un breve delay
    setTimeout(() => {
      this.isNavigating = false;
    }, 500);
  }

  private updateUrlWithPage(page: number) {
    const currentParams = this._activatedRoute.snapshot.queryParams;

    this._router.navigate([], {
      relativeTo: this._activatedRoute,
      queryParams: {
        ...currentParams,
        page: page
      },
      queryParamsHandling: 'merge',
      replaceUrl: true
    });
  }

  // Event handler per il cambio slide di swiper
  slideChanges(event: any) {
    if (this.isNavigating) return;

    const swiperInstance = this.swiperContainer.nativeElement.swiper;
    const activeIndex = swiperInstance.realIndex;
    const newPage = activeIndex + 1;

    console.log(`🔄 Slide changed to index ${activeIndex}, page ${newPage}`);

    // Evita ricarichi inutili
    if (newPage !== this.currentPage && newPage >= 1 && newPage <= this.totalPages) {
      this.currentPage = newPage;
      this.updateUrlWithPage(newPage);
      this.loadPageItems(newPage);
    }
  }

  // Navigation methods
  openRootCategory(event: any) {
    console.log("Opening root category:", event);

    // Aggiorna lo stato locale
    this.rootId = event.idRootCategory;
    this.categoryId = null;

    // Aggiorna l'URL
    this._router.navigate([], {
      relativeTo: this._activatedRoute,
      queryParams: {
        rootId: this.rootId,
        page: 1
      },
      queryParamsHandling: 'merge',
      replaceUrl: true
    });

    // Imposta la vista root a false, caricando le categorie figlie
    this.showCategories(1);
  }


  openCategory(event: any) {
    console.log("Opening category:", event);
    this.catalogService.setActiveCategory(event);
    this._router.navigate([`/private/catalog/products-carousel/${event.idCategory}`], {
      queryParams: {
        rootId: this.rootId,
        page: 1
      }
    });
  }

  // Data loading methods
  async showRootsCategories() {
    console.log('🏠 Showing root categories');

    this.catalogService.setIntoSubcategory(false);
    this.catalogService.setRootView(true);

    let rootCategories = [];

    this._store.pipe(select('rootCategories')).subscribe(res => {
      rootCategories = res;
      console.log(`Found ${rootCategories.length} root categories in store`);
    }).unsubscribe();

    if (!(rootCategories && rootCategories.length > 0)) {
      let allCategories: Category[] = [];
      this._store.pipe(select('categories')).subscribe(res => {
        allCategories = [...res];
        rootCategories = allCategories.filter(cat => /\b(?:it;)(\d+)\b(?!;[\d])/.test(cat.idApp));
      }).unsubscribe();

      // Calcola il conteggio delle sottocategorie per le root categories
      const rootCategoriesWithCount = this.calculateSubcategoriesCountForRootCategories(rootCategories, allCategories);

      this._store.dispatch(setRootCategories({ rootCategories: rootCategoriesWithCount }));
    }

    // Setup per root categories
    this.allCategories = rootCategories;
    this.totalItems = this.allCategories.length;
    this.recalculatePagination();
    this.currentPage = 1;

    // Reset cache e carica la prima pagina
    this.pageCache.clear();
    this.loadPageItems(1);

    console.log(`✅ Root categories loaded: ${this.totalItems} items, ${this.totalPages} pages`);
  }

  async showCategories(page: number = 1) {
    console.log(`📂 Showing categories - Page: ${page}`);

    this.catalogService.setRootView(false);
    this.catalogService.setIntoSubcategory(true);

    //this.isLoading = true;

    try {
      // Carica tutte le categorie se non già caricate
      console.log(this.allCategories);
      await this.loadAllCategories(this.rootId);

      // Setup paginazione
      this.totalItems = this.allCategories.length;
      this.recalculatePagination();

      // Assicurati che la pagina richiesta sia valida
      const validPage = Math.max(1, Math.min(page, this.totalPages));

      // Reset cache
      this.pageCache.clear();

      // Precarica immediatamente un range di pagine attorno a quella richiesta
      const preloadRange = 5; // Precarica ±5 pagine inizialmente
      for (let i = -preloadRange; i <= preloadRange; i++) {
        const pageToLoad = validPage + i;
        if (pageToLoad >= 1 && pageToLoad <= this.totalPages) {
          this.loadSinglePageToCache(pageToLoad);
        }
      }

      // Imposta la pagina corrente
      this.currentPage = validPage;
      this.items = this.pageCache.get(validPage) || [];
      console.log(this.items)
      // Aggiorna URL se necessario
      if (validPage !== page) {
        this.updateUrlWithPage(validPage);
      }

      // Forza il rilevamento dei cambiamenti
      this._cd.detectChanges();

      // Aggiorna swiper dopo il rendering
      setTimeout(() => {
        this.updateSwiperToPage(validPage);
      }, 100);

    } catch (error) {
      console.error('Error loading categories:', error);
    } finally {
      this.isLoading = false;
    }
  }

  private async loadAllCategories(rootId: string) {
    console.log(`📦 Loading all categories for rootId: ${rootId}`);

    let categories = [];
    this._store.pipe(select('categories')).subscribe(res => categories = [...res]).unsubscribe();

    try {
      const childCategories = await this._syncroUnifiedService.getCategoriesByParent(rootId);
      console.log(`Found ${childCategories.length} child categories`);

      let candidateCategories: Category[] = [];

      if (childCategories.length > 0) {
        candidateCategories = childCategories.filter((item, index) => {
          if (this.activeCardView === CardType.SMALL) {
            // Per le card small, mostra le categorie che possono essere mostrate o i prodotti
            return this.canBeShownInArray(item, index, childCategories) || item.isProduct;
          } else {
            // Per le altre card view, mostra solo i prodotti
            return item.isProduct;
          }
        });
      } else {
        // Fallback
        candidateCategories = categories.filter((item) => {
          return item.idApp.toString().includes(rootId.toString()) &&
            item.idApp.toString() != rootId.toString() &&
            item.level > 2;
        });
      }

      // Applica il filtraggio basato sui tag del cliente
      console.log(`🔍 Applying tag filters to ${candidateCategories.length} candidate categories`);
      this.allCategories = await this._categoryService.getFilteredCategoriesByTags(candidateCategories);

      console.log(`✅ After filtering: ${this.allCategories.length} categories visible`);

    } catch (error) {
      console.error('Error loading categories:', error);
      // Fallback senza filtraggio in caso di errore
      this.allCategories = categories.filter((item) => {
        return item.idApp.toString().includes(rootId.toString()) &&
          item.idApp.toString() != rootId.toString() &&
          item.level > 2;
      });
    }

    console.log(`✅ Loaded ${this.allCategories.length} categories total`);
  }

  private canBeShown(id: string): boolean {
    // Trova l'indice dell'item corrente nell'array completo delle categorie
    let allCategories: Category[] = [];
    this._store.pipe(select('categories')).subscribe(res => allCategories = [...res]).unsubscribe();
    
    const currentIndex = allCategories.findIndex(category => category.id.toString() === id.toString());
    
    // Se non trova l'item o è l'ultimo, può essere mostrato
    if (currentIndex === -1 || currentIndex === allCategories.length - 1) {
      return true;
    }
    
    // Controlla se l'item successivo è un prodotto
    const nextItem = allCategories[currentIndex + 1];
    return nextItem.isProduct === true;
  }

  private canBeShownInArray(item: Category, index: number, array: Category[]): boolean {
    // Se è l'ultimo item nell'array, può essere mostrato
    if (index === array.length - 1) {
      return true;
    }
    
    // Controlla se l'item successivo è un prodotto
    const nextItem = array[index + 1];
    return nextItem.isProduct === true;
  }

  async showProducts() {
    console.log('📦 Showing products');
    this.catalogService.setRootView(false);
    this.catalogService.setIntoSubcategory(true);
  }

  // Utility methods
  public isFavorite(idCategory: string): boolean {
    const favorites: Favorite[] = this.favorites.filter((favorite) =>
      favorite.customerUid.toString() === this.catalogService.userUid.toString() &&
      favorite.idCategory.toString() === idCategory
    );
    return favorites.length === 1;
  }

  trackById(index: number, item: CatalogCard): string {
    return item.id;
  }

  // Genera array di slide virtuali per swiper
  getVirtualSlides(): number[] {
    return Array.from({ length: this.totalPages }, (_, i) => i);
  }

  // Array helper per template
  createArray(length: number): number[] {
    return Array(length).fill(0);
  }

  // Main dispatch method
  dispatch() {
    console.log("🚀 Dispatching...");

    // Load favorites
    this._store.pipe(
      select('favorites'),
      takeUntil(this.destroy$)
    ).subscribe(res => {
      this.favorites = [...res].filter((item: Favorite) =>
        item.customerUid === this.catalogService.userUid
      );
    });

    // Handle route parameters
    this._activatedRoute.queryParams.pipe(
      takeUntil(this.destroy$)
    ).subscribe(async params => {
      console.log('📍 Route params:', params);

      if (params.search) {
        const searchId = params.search;
        this.openCategoryPage(searchId);
      } else {

        if (params.rootId) {
          const newRootId = params.rootId;

          const page = parseInt(params.page) || 1;
          const categoryId = params.categoryId || null;

          // Se è cambiata la root o vogliamo forzare il reload
          const isNewRoot = this.rootId !== newRootId;

          this.rootId = newRootId;
          this.categoryId = categoryId;

          if (categoryId) {
            this.showProducts();
          } else {
            if (isNewRoot) {
              // Azzeriamo le categorie per forzare il reload
              this.allCategories = [];
            }
            console.log(`🔍 Showing categories for rootId: ${this.rootId}`);
            await this.showCategories(page);
          }
        }
        else {
          this.showRootsCategories();
        }
      }

    });
  }

  openCategoryPage(searchId: string) {
    console.log(`🔍 Searching category with ID: ${searchId}`);

    // Trova l'indice della categoria con l'ID specificato
    let categories: Category[] = [];
    this._store.pipe(select('categories'), takeUntil(this.destroy$)).subscribe(res => {
      categories = [...res];
      console.log(categories);
    }).unsubscribe();

    const index = categories.findIndex((category: Category) => {
      return category.id.toString() === searchId.toString();
    });

    if (index === -1) {
      console.warn(`⚠️ Category with ID ${searchId} not found`);
      return 0; // Restituisce 0 se non trovata
    }

    const pageNumber = Math.floor(index / this.itemsPerPage) + 1;

    let category = categories[index];
    this.rootId = category.idRootCategory;
    if (this.catalogService.testIsRoot(category.idApp)) {
      console.log(`✅ Category ${category.id} is a root category`);
      this.showRootsCategories();
    } else {
      console.log(`✅ Category ${category.id} is a subcategory`);
      this.showCategories(pageNumber)
    }
  }


  getEmptySlots(): any[] {
    const count = Math.max(0, (this.columns * this.rows) - this.items.length);
    return this.createArray(count);
  }

  swiperTrackByFn(index: number, item: any): any {
    return item;
  }

  /**
   * Calcola il numero di sottocategorie per una root category
   */
  private calculateSubcategoriesCount(rootCategory: Category, allCategories: Category[]): number {
    try {
      if (!rootCategory.idApp) {
        return 0;
      }

      // Conta le categorie che hanno come parent questa root category
      const subcategoriesCount = allCategories.filter(cat => {
        return cat.idApp &&
               cat.idApp !== rootCategory.idApp &&
               cat.idApp.includes(rootCategory.idApp) &&
               !cat.isProduct; // Contiamo solo le categorie, non i prodotti
      }).length;

      console.log(`🔢 calculateSubcategoriesCount: Root category ${rootCategory.id} ha ${subcategoriesCount} sottocategorie`);
      return subcategoriesCount;
    } catch (error) {
      console.error(`❌ Errore nel calcolo delle sottocategorie per root category ${rootCategory.id}:`, error);
      return 0;
    }
  }

  /**
   * Calcola il conteggio delle sottocategorie per tutte le root categories
   */
  private calculateSubcategoriesCountForRootCategories(rootCategories: Category[], allCategories: Category[]): Category[] {
    try {
      console.log(`🔢 Calcolo sottocategorie per ${rootCategories.length} root categories`);

      const updatedRootCategories = rootCategories.map((rootCategory) => {
        const subcategoriesCount = this.calculateSubcategoriesCount(rootCategory, allCategories);
        return {
          ...rootCategory,
          subcategoriesCount: subcategoriesCount
        };
      });

      console.log(`✅ Calcolo sottocategorie completato per ${updatedRootCategories.length} root categories`);
      return updatedRootCategories;
    } catch (error) {
      console.error('❌ Errore nel calcolo delle sottocategorie per le root categories:', error);
      return rootCategories; // Ritorna le categorie originali in caso di errore
    }
  }

}