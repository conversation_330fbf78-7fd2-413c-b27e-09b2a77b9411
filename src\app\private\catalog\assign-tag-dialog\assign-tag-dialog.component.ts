import { CommonModule } from '@angular/common';
import { Component, Inject, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewEncapsulation } from '@angular/core';
import { <PERSON><PERSON><PERSON>on, MatButtonModule } from '@angular/material/button';
import { MAT_DIALOG_DATA, MatDialog, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { CatalogsService } from '../../services/catalog.service';
import { Tag, TypologyContent } from '../../model/sector-hierarchy-response';
import { FormsModule } from '@angular/forms';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { catchError, concatMap, finalize, forkJoin, from, map, of } from 'rxjs';

// Use Tag directly if it already contains 'checked' property
export interface TagWithCheck extends Tag {
  checked?: boolean;
}

@Component({
  selector: 'app-assign-tag-dialog',
  imports: [MatProgressSpinnerModule,FormsModule ,TranslateModule, MatDialogModule, CommonModule, MatButtonModule, MatSelectModule, MatExpansionModule, MatSlideToggleModule, MatFormFieldModule, MatInputModule,MatIcon],
  templateUrl: './assign-tag-dialog.component.html',
  styleUrl: './assign-tag-dialog.component.scss' 
})
export class AssignTagDialogComponent implements OnInit, OnDestroy {

  private initialAssignedKeys: Set<string> = new Set();
  loadingPage = false;
  result: TypologyContent | undefined;
  sectors: TagWithCheck[] = [];
  action: 'ADD' | 'REMOVE' | 'MANAGE' | undefined;

   constructor(
    private translate: TranslateService,
    private catalogsService: CatalogsService,
    private dialogRef: MatDialogRef<AssignTagDialogComponent>,
    private dialog: MatDialog,
    @Inject(MAT_DIALOG_DATA) public data: {action: 'ADD' | 'REMOVE' | 'MANAGE', idSubCategory: number[], idCatalog: number, categoryName: string },
    private readonly snackBar: MatSnackBar,
  ) {}



  ngOnInit(): void {
    // Blocca lo scroll della pagina quando il dialogo è aperto
    document.body.style.overflow = 'hidden';
    this.loadAssignedTagsThenHierarchy();
    this.action = this.data.action;
  }


    ngOnDestroy(): void {
    // Ripristina lo scroll della pagina quando il dialogo viene chiuso
      document.body.style.overflow = '';
    }

  /**
   * Carica i tag assegnati e poi la gerarchia dei settori.
   * Se non ci sono tag assegnati, carica comunque la gerarchia senza filtri.
   */
  private loadAssignedTagsThenHierarchy(): void {
    const idSubCategory = this.data.idSubCategory;

    if (Array.isArray(idSubCategory) && idSubCategory.length > 1) {
      // Caso MULTI selezione: apri gerarchia vuota senza selezioni
      this.initialAssignedKeys = new Set(); // nessuna selezione
      this.loadSectorHierarchy(); // senza parametri → nessun filtro
    } else {
      // Caso singolo (array di 1 o singolo valore)
      const singleId = Array.isArray(idSubCategory) ? idSubCategory[0] : idSubCategory;

      this.catalogsService.getDatacolCategoryTags({
        idSubCategory: singleId,
        idCatalog: this.data.idCatalog
      }).subscribe({
        next: (res) => {
          if (res.status === 'OK') {
            const assignedKeys = res.content.tags.map(t => t.keyTag);
            this.initialAssignedKeys = new Set(assignedKeys);

            this.loadSectorHierarchy(assignedKeys);
          } else {
            this.showError();
            this.loadSectorHierarchy();
          }
        },
        error: () => {
          this.showError();
          this.loadSectorHierarchy();
        }
      });
    }
  }

  /**
   * Carica la gerarchia dei settori e spunta i tag assegnati se presenti.
   * Se non ci sono tag assegnati, carica comunque la gerarchia senza filtri.
   */
  private loadSectorHierarchy(assignedKeys: string[] = []): void {
    this.catalogsService.getSectorHierarchy({ idCatalog: this.data.idCatalog }).subscribe({
      next: (res) => {
        this.result = res;
        this.sectors = res.tags as TagWithCheck[];

        // Spunta i tag assegnati se presenti
        if (assignedKeys.length > 0) {
          this.setCheckedTags(this.sectors, assignedKeys);
        }
      },
      error: () => this.showError()
    });
  }


  private showError(): void {
    const msg = this.translate.instant('ERRORS.ERROR_LOADING_RESULTS');
    const btn = this.translate.instant('BUTTONS_SNACKBAR.CLOSE');
    this.snackBar.open(msg, btn, { duration: 3000 });
  }

  private setCheckedTags(tags: Tag[], assignedKeys: string[]): void {
    for (const tag of tags) {
      tag.checked = assignedKeys.includes(tag.keyTag);
      if (tag.subTags?.length) {
        this.setCheckedTags(tag.subTags, assignedKeys);
      }
    }
  }

  confirm(): void {
    if (this.isAllSectorsSelected()) {
      // TODO: Implement confirmation dialog for all sectors
      console.warn('All sectors selected - confirmation dialog not implemented');
      this.assignTag();
    } else {
      // Se non è selezionato "Tutti i settori", procedi direttamente
      this.assignTag();
    }
  }


  /**
   * Funzione per raccogliere solo i tag foglia che sono selezionati
   * Un tag foglia è un tag che non ha figli e il cui stato checked è true
  */
  private collectTagsByCheckedStatus(
    nodes: any[],
    action: 'ADD' | 'REMOVE' | 'MANAGE',
    selected = new Set<string>(),
    deselected = new Set<string>()
  ): { selected: Set<string>, deselected: Set<string> } {
    for (const node of nodes) {
      const hasChildren = node.subTags?.length > 0;

      if (node.checked) {
        if (action === 'ADD' && !hasChildren) {
          selected.add(node.keyTag); // Solo foglia per aggiunta
        } else if (action === 'REMOVE' && hasChildren) {
          selected.add(node.keyTag); // Anche padre per rimozione
        } else if (action === 'MANAGE' && !hasChildren) {
          selected.add(node.keyTag); // Come ADD
        }
      }

      if (!node.checked) {
        deselected.add(node.keyTag); // Per gestione deselection
      }

      if (hasChildren) {
        this.collectTagsByCheckedStatus(node.subTags, action, selected, deselected);
      }
    }

    return { selected, deselected };
  }

  /**
   * Funzione per assegnare i tag selezionati
   * Raccoglie i tag selezionati e quelli da rimuovere, quindi esegue le operazioni di aggiunta/rimozione
   */
  private assignTag(): void {
    this.loadingPage = true;
    const errorMessages: string[] = [];

    const { selected, deselected } = this.collectTagsByCheckedStatus(this.sectors, this.data.action);
    const selectedKeys = new Set(selected);
    const deselectedKeys = new Set(deselected);

    const idSubCategories = Array.isArray(this.data.idSubCategory)
      ? this.data.idSubCategory
      : [this.data.idSubCategory];

    const operations = [];

    if (this.data.action === 'MANAGE') {
      // Aggiunge i nuovi flag e rimuovi i deselected
      const toAdd = [...selectedKeys].filter(key => !this.initialAssignedKeys.has(key));
      const toRemove = [...deselectedKeys].filter(key => this.initialAssignedKeys.has(key));

      for (const idSub of idSubCategories) {
        for (const keyTag of toAdd) {
          operations.push(() => this.addTag(String(idSub), keyTag, errorMessages));
        }
        for (const keyTag of toRemove) {
          operations.push(() => this.removeTag(String(idSub), keyTag, errorMessages));
        }
      }

    } else if (this.data.action === 'ADD') {
      // Aggiunge i tag selezionati (che non erano già presenti)
      const toAdd = [...selectedKeys].filter(key => !this.initialAssignedKeys.has(key));

      for (const idSub of idSubCategories) {
        for (const keyTag of toAdd) {
          operations.push(() => this.addTag(String(idSub), keyTag, errorMessages));
        }
      }
    } else if (this.data.action === 'REMOVE') {
      // Rimuove solo quelli che l’utente ha selezionato 
      const toRemove = [...selectedKeys].filter(key => !this.initialAssignedKeys.has(key));

      for (const idSub of idSubCategories) {
        for (const keyTag of toRemove) {
          operations.push(() => this.removeTag(String(idSub), keyTag, errorMessages));
        }
      }
    }


    if (operations.length === 0) {
      this.loadingPage = false;
      this.dialogRef.close({ added: [], removed: [] });
      return;
    }

    from(operations).pipe(
      concatMap(op => op()),
      finalize(() => {
        this.loadingPage = false;
        if (this.data.action !== 'REMOVE') {
          this.initialAssignedKeys = selectedKeys;
        }

        if (errorMessages.length) {
          this.snackBar.open(
            errorMessages.join('\n'),  
            'Chiudi',
            {
              duration: 50000,
              verticalPosition: 'bottom',
              panelClass: ['snackbar-multiline'],
            }
          );
        } else {
          this.snackBar.open(
            this.translate.instant('MESSAGES_SNACKBAR.TAGS_ASSIGNED_SUCCESS'),
            this.translate.instant('BUTTONS_SNACKBAR.CLOSE'),
            { duration: 3000 }
          );
        }

        this.dialogRef.close({ added: this.data.action === 'REMOVE' ? [] : [...selectedKeys], removed: this.data.action === 'REMOVE' ? [...selectedKeys] : [] });
      })
    ).subscribe();
  }

  private addTag(idSub: string, keyTag: string, errorMessages: string[]) {
    return this.catalogsService.addDatacolCategoryTag({
      idCatalog: this.data.idCatalog,
      idSubCategory: Number(idSub),
      keyTag,
    }).pipe(
      map(response => {
        const errorResult = response.results?.find(r => r.esit?.status === 'KO');
        if (errorResult) {
          const code = errorResult.esit?.error?.code;
          const categoryName = this.data.categoryName;
          let msg = '';
          if (code === '404') {
            msg = this.translate.instant('ERRORS.NOT_FOUND', { idSub, categoryName, keyTag });
          } else if (code === '409') {
            msg = this.translate.instant('ERRORS.ALREADY_INSERTED', { idSub, categoryName, keyTag });
          } else {
            msg = this.translate.instant('ERRORS.GENERIC', { idSub, categoryName, keyTag });
          }
          errorMessages.push(msg);
        }
        return response;
      })
    );
  }

  private removeTag(idSub: string, keyTag: string, errorMessages: string[]) {
    return this.catalogsService.removeDatacolCategoryTag({
      idCatalog: this.data.idCatalog,
      idSubCategory: Number(idSub),
      keyTag,
    }).pipe(
      catchError(err => {
        const code = err?.status || 'UNKNOWN';
        const categoryName = this.data.categoryName;
        let errorMsg = '';
        if (code === 404) {
          errorMsg = this.translate.instant('ERRORS.NOT_FOUND', { idSub, categoryName, keyTag });
        } else if (code === 409) {
          errorMsg = this.translate.instant('ERRORS.ALREADY_INSERTED', { idSub, categoryName, keyTag });
        } else {
          errorMsg = this.translate.instant('ERRORS.GENERIC', { idSub, keyTag });
        }
        errorMessages.push(errorMsg);
        return of(null);
      })
    );
  }


  // Imposta lo stato checked ricorsivamente agli altri nodi figli
  setCheckedRecursive(node: any, checked: boolean): void {
    node.checked = checked;
    node.indeterminate = false;

    if (node.subTags && node.subTags.length) {
      node.subTags.forEach((child: any) => this.setCheckedRecursive(child, checked));
    }
  }

  // Aggiorna ricorsivamente i genitori risalendo l'albero
  updateParentsCheckStatus(node: any, rootNodes: any[]): void {
    const parent = this.findParentNode(node, rootNodes);
    if (!parent) return;

    const someChildrenChecked = parent.subTags.some((child: any) => child.checked);

    // Se almeno un figlio è selezionato, seleziona anche il padre
    if (someChildrenChecked) {
      parent.checked = true;
      parent.indeterminate = false;
    } else {
      parent.checked = false;
      parent.indeterminate = false;
    }

    // Continua a risalire l'albero
    this.updateParentsCheckStatus(parent, rootNodes);
  }


  // Cerca il genitore di un nodo nell'albero partendo da rootNodes
  findParentNode(targetNode: any, nodes: any[], parent: any = null): any {
    for (const node of nodes) {
      if (node === targetNode) {
        return parent;
      }
      if (node.subTags) {
        const found = this.findParentNode(targetNode, node.subTags, node);
        if (found) return found;
      }
    }
    return null;
  }



  onToggle(node: any, checked: boolean, rootNodes: any[]): void {
    if (this.isUniversalTag(node) && checked) {
      // Se si seleziona il tag universale gli altri vengono nascosti
      rootNodes.forEach(sector => {
        if (!this.isUniversalTag(sector)) {
          this.setCheckedRecursive(sector, false);
        }
      });
    } else if (!this.isUniversalTag(node) && checked) {
      // Se si deseleziona il tag universale tutti gli altri settori vengono mostrati
      rootNodes.forEach(sector => {
        if (this.isUniversalTag(sector)) {
          this.setCheckedRecursive(sector, false);
        }
      });
    }

    // Aggiorna lo stato del nodo cliccato
    this.setCheckedRecursive(node, checked);
    this.updateParentsCheckStatus(node, rootNodes);
  }

  get universalTagId(): number | null {
    return this.result?.universalTag?.idTag ?? null;
  }

  isUniversalTag(node: any): boolean {
    return node?.idTag === this.universalTagId;
  }

  isAllSectorsSelected(): boolean {
    return this.sectors.some(sector => sector.idTag === this.result?.universalTag?.idTag && sector.checked);
  }

}