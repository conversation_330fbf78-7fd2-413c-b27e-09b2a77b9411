<app-navigation-queue [isFavorites]="showFavorites" [customerName]="customerName"
  (goToCategory)="goToCategory($event)"></app-navigation-queue>
@if(catalogService.catalogsAreLoaded | async) {

<div class="container">
  @if(catalogService.isPaginatedView || catalogService.isRootView) {
  <app-paginated-catalog></app-paginated-catalog>
  }
  @if(!catalogService.isRootView && catalogService.isInfiniteView) {
  <app-infinite-catalog>
  </app-infinite-catalog>
  }
</div>
}

<div *ngIf="prevCategory || nextCategory" class="category-navigation">
  <div class="prev-category">
    <div *ngIf="prevCategory" (click)="goToCategorySiblings(prevCategory)">
      <ion-icon name="caret-back-circle"></ion-icon><span [innerHTML]="prevCategory.name"></span>
    </div>
  </div>
  <div class="next-category">
    <div *ngIf="nextCategory" (click)="goToCategorySiblings(nextCategory)">
      <span [innerHTML]="nextCategory.name"></span><ion-icon name="caret-forward-circle"></ion-icon>
    </div>
  </div>
</div>

<div
  *ngIf="(catalogService.isPaginatedView && !catalogService.isRootView && currentPageIndex === (totalPages-1) && !!nextRootCategory) "
  class="category-navigation">
  <div class="prev-category">
  </div>
  <div class="next-category">
    <div (click)="goToNextCategory(nextRootCategory)">
      <span [innerHTML]="nextRootCategory.next.name"></span><ion-icon name="caret-forward-circle"></ion-icon>
    </div>
  </div>
</div>

<div *ngIf="customerName" class="customer-name">
  <ng-container *ngIf="!catalogService.isProspect"><img src="../../../assets/svg/person.svg" /><span
      [innerHTML]="customerName"></span></ng-container>
  <ng-container *ngIf="catalogService.isProspect">{{ 'CATALOG.PROSPECT' | translate }} / <span
      [innerHTML]="customerName"></span></ng-container>
</div>

<app-search *ngIf="isFilterShown" [viewType]="'catalog'" [simpleView]="true"
  (closeSearch)="closeProductSearch($event)"></app-search>
<app-prices *ngIf="isPricesShown" [@datacolPopup] [datacolCategoryName]="selDatacolCategoryName"
  [products]="selProducts" (addToCart)="addOneToCart($event)" (closePopover)="hidePrices()"></app-prices>
<app-subcategories-preview *ngIf="isSubcatPreviewShown" [subcategories4Preview]="subcategories4Preview"
  (close)="closeSubcategoriesPreview()" (open)="openSubcategory($event)"></app-subcategories-preview>
<app-navigation-panel #panelComponent [ngClass]="{'visible': showNavigator}" (hideNavigator)="showNavigator = false"
  (navToPage)="navToPage($event)"></app-navigation-panel>

<app-right-catalog-toolbar [isShowed]="showNavigator" (showNavigator)="onShowNavigator($event)"
  (switchCardView)="switchCardView($event)" (openTagFilterDialog)="openTagFilterDialog()"></app-right-catalog-toolbar>