import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { BasicService } from '../../service/basic.service';
import { 
  SectorHierarchyResponse, 
  TypologyContent, 
  DatacolCategoryTagsResponse,
  AddRemoveTagRequest,
  AddRemoveTagResponse
} from '../model/sector-hierarchy-response';

@Injectable({
  providedIn: 'root'
})
export class CatalogsService extends BasicService {

  constructor() {
    super();
  }

  /**
   * Ottiene la gerarchia dei settori per un catalogo
   * @param params Parametri contenenti idCatalog
   * @returns Observable<TypologyContent>
   */
  getSectorHierarchy(params: { idCatalog: number }): Observable<TypologyContent> {
    const url = `/appcatalogs/getSectorHierarchy?idCatalog=${params.idCatalog}`;
    console.log('🔗 CatalogsService.getSectorHierarchy - URL:', url);

    return new Observable(observer => {
      this.basicGet(url, false).then((response: any) => {
        const typedResponse = response as SectorHierarchyResponse;
        if (typedResponse.status === 'OK') {
          observer.next(typedResponse.content);
        } else {
          observer.error(typedResponse.error || 'Error loading sector hierarchy');
        }
        observer.complete();
      }).catch(error => {
        observer.error(error);
      });
    });
  }

  /**
   * Ottiene i tag assegnati a una categoria
   * @param params Parametri contenenti idSubCategory e idCatalog
   * @returns Observable<DatacolCategoryTagsResponse>
   */
  getDatacolCategoryTags(params: { idSubCategory: number; idCatalog: number }): Observable<DatacolCategoryTagsResponse> {
    const url = `/appcatalogs/getDatacolCategoryTags?idSubCategory=${params.idSubCategory}&idCatalog=${params.idCatalog}`;
    console.log('🔗 CatalogsService.getDatacolCategoryTags - URL:', url);

    return new Observable(observer => {
      this.basicGet(url, false).then((response: any) => {
        observer.next(response as DatacolCategoryTagsResponse);
        observer.complete();
      }).catch(error => {
        observer.error(error);
      });
    });
  }

  /**
   * Aggiunge un tag a una categoria
   * @param request Richiesta contenente idCatalog, idSubCategory e keyTag
   * @returns Observable<AddRemoveTagResponse>
   */
  addDatacolCategoryTag(request: AddRemoveTagRequest): Observable<AddRemoveTagResponse> {
    const url = `/appcatalogs/addDatacolCategoryTag`;
    console.log('🔗 CatalogsService.addDatacolCategoryTag - URL:', url, 'Request:', request);

    return new Observable(observer => {
      this.basicPost(request, url, false).then((response: any) => {
        observer.next(response as AddRemoveTagResponse);
        observer.complete();
      }).catch(error => {
        observer.error(error);
      });
    });
  }

  /**
   * Rimuove un tag da una categoria
   * @param request Richiesta contenente idCatalog, idSubCategory e keyTag
   * @returns Observable<AddRemoveTagResponse>
   */
  removeDatacolCategoryTag(request: AddRemoveTagRequest): Observable<AddRemoveTagResponse> {
    const url = `/appcatalogs/removeDatacolCategoryTag`;
    console.log('🔗 CatalogsService.removeDatacolCategoryTag - URL:', url, 'Request:', request);

    return new Observable(observer => {
      this.basicPost(request, url, false).then((response: any) => {
        observer.next(response as AddRemoveTagResponse);
        observer.complete();
      }).catch(error => {
        observer.error(error);
      });
    });
  }
}
