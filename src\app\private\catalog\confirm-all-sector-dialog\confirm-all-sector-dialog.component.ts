import { CommonModule } from '@angular/common';
import { Component, Inject } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'app-confirm-all-sector-dialog',
imports: [FormsModule ,TranslateModule, MatDialogModule, CommonModule, MatButtonModule, MatSelectModule, MatExpansionModule, MatSlideToggleModule, MatFormFieldModule, MatInputModule,MatIcon],
  templateUrl: './confirm-all-sector-dialog.component.html',
  styleUrl: './confirm-all-sector-dialog.component.scss'
})
export class ConfirmAllSectorDialogComponent {
confirm() {
throw new Error('Method not implemented.');
}

   constructor(
    public dialogRef: MatDialogRef<ConfirmAllSectorDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { message: string }
  ) {}

  onCancel(): void {
    this.dialogRef.close(false);
  }

  onConfirm(): void {
    this.dialogRef.close(true);
  }

}
