export interface Tag {
  idTag: number;
  keyTag: string;
  description: string;
  subTags: Tag[];
  checked?: boolean;
}

export interface TypologyContent {
  tags: Tag[];
  universalTag?: Tag;
}

export interface SectorHierarchyResponse {
  status: string;
  content: TypologyContent;
  error?: any;
}

export interface DatacolCategoryTag {
  keyTag: string;
  description?: string;
}

export interface DatacolCategoryTagsResponse {
  status: string;
  content: {
    tags: DatacolCategoryTag[];
  };
  error?: any;
}

export interface AddRemoveTagRequest {
  idCatalog: number;
  idSubCategory: number;
  keyTag: string;
}

export interface AddRemoveTagResponse {
  status: string;
  results?: Array<{
    esit?: {
      status: string;
      error?: {
        code: string;
        message?: string;
      };
    };
  }>;
  error?: any;
}
