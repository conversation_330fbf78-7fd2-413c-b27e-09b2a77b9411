  <div *ngIf="loadingPage" class="dat-overlay-loader">
    <mat-progress-spinner mode="indeterminate"></mat-progress-spinner>
  </div>


<h2 mat-dialog-title class="dat-dialog-title">
  {{
    (action === 'ADD' ? 'POPUP.ADD_TAGS_TITLE' :
     action === 'REMOVE' ? 'POPUP.REMOVE_TAGS_TITLE' :
     action === 'MANAGE' ? 'POPUP.MANAGE_TAGS_TITLE' :
     '') | translate
  }}
</h2>




<button mat-icon-button class="close-btn" mat-dialog-close>
  <mat-icon>close</mat-icon>
</button>

<hr class="dat-hr" />

<mat-dialog-content>
  <div *ngFor="let sector of sectors">
    <div *ngIf="isAllSectorsSelected() ? sector.idTag === result?.universalTag?.idTag : true">
      <mat-accordion>
        <mat-expansion-panel class="dialog-accordion-panel" [hideToggle]="!sector.subTags.length" [ngClass]="{'unclickable-panel': !sector.subTags.length}">
          <mat-expansion-panel-header>
            <mat-panel-title class="dialog-accordion-title">
              <mat-slide-toggle class="dialog-toggle"  (change)=" setCheckedRecursive(sector, $event.checked);"  [(ngModel)]="sector.checked" (click)="$event.stopPropagation()"></mat-slide-toggle>
              {{ sector.description }}
            </mat-panel-title>
          </mat-expansion-panel-header>

          <!-- Attività -->
          <mat-accordion *ngIf="sector.subTags?.length" >
            <mat-expansion-panel class="dialog-accordion-panel" *ngFor="let activity of sector.subTags" [hideToggle]="!activity.subTags.length" [ngClass]="{'unclickable-panel': !activity.subTags.length}">
              <mat-expansion-panel-header>
                <mat-panel-title class="dialog-accordion-title">
                  <mat-slide-toggle class="dialog-toggle"  (change)="
                  setCheckedRecursive(activity, $event.checked);
                  updateParentsCheckStatus(activity, sectors);
                  "[(ngModel)]="activity.checked"
                  (click)="$event.stopPropagation()"
                  ></mat-slide-toggle>
                  {{ activity.description }}
                </mat-panel-title>
              </mat-expansion-panel-header>

              <!-- Professioni -->
              <div *ngIf="activity.subTags?.length" >
                <p *ngFor="let profession of activity.subTags">
                  <mat-slide-toggle
                  class="dialog-toggle profession-toggle"
                  (change)="onToggle(profession, $event.checked, sectors)"
                  (click)="$event.stopPropagation()"
                  [checked]="profession.checked">
                </mat-slide-toggle>
                <span class="toggle-prosfession-label">{{ profession.description }}</span>
                </p>
              </div>
            </mat-expansion-panel>
          </mat-accordion>
        </mat-expansion-panel>
      </mat-accordion>
    </div>
  </div>
</mat-dialog-content>

<mat-dialog-actions class="dialog-actions">
  <button class="secondary-button" mat-raised-button mat-dialog-close>
    {{ 'POPUP.CANCEL' | translate }}
  </button>
  <button class="primary-button" mat-raised-button (click)="confirm()">
    {{ 'POPUP.APPLY' | translate }}
  </button>
</mat-dialog-actions>
