//   ACCORDION
.dialog-accordion-panel {
    background-color: var(--dialog-accordion-bg);
    border-radius: 5px;
    border: 1px solid var(--dialog-accordion-bg);
    margin-top: 1em;
}

.mat-expansion-panel {
    background-color: var(--dialog-accordion-bg) !important;
}

.dialog-accordion-title{
    color: var(--dialog-accordion-color);
}



.dialog-toggle{
    margin-right: 1em;
}

.close-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    border: none;
    color: var(--icon-color); 
    font-size: 24px; 
}

.dialog-actions {
    margin-top: 1em;
    margin-bottom: 1em;
}

.mat-expansion-panel:not([class*=mat-elevation-z]) {
    box-shadow: none;
}

.mat-expansion-panel-body {
    padding: 10px 24px 16px;
}

.mat-accordion>.mat-expansion-panel-spacing:last-child, .mat-accordion>*:last-child:not(.mat-expansion-panel) .mat-expansion-panel-spacing {
    margin-top: 18px !important;
}


.unclickable-panel {
  pointer-events: none;
}

.toggle-prosfession-label{
  margin-right: 10em; 
  margin-right: 20em;
}

.dialog-toggle.profession-toggle{
    margin-left: 1.5em;
}