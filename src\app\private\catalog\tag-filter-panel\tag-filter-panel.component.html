<!-- Loading overlay -->
<div *ngIf="isLoading" class="dat-overlay-loader">
  <mat-progress-spinner mode="indeterminate"></mat-progress-spinner>
</div>

<!-- Header -->
<div class="filter-panel-header">
  <h3 class="dat-dialog-title">
    {{ 'TAG_FILTER.TITLE' | translate }}
  </h3>

  <!-- Filtering toggle -->
  <div class="filter-toggle-header">
    <mat-slide-toggle
      [(ngModel)]="isFilteringEnabled"
      (change)="toggleFiltering()"
      class="dialog-toggle">
    </mat-slide-toggle>
    <span>{{ 'TAG_FILTER.ENABLE_FILTERING' | translate }}</span>
  </div>
</div>

<hr class="dat-hr" />

<!-- Current filter summary -->
<div class="current-filter-summary" *ngIf="currentFilter && isFilteringEnabled">
  <h4>{{ 'TAG_FILTER.CURRENT_FILTER' | translate }}</h4>
  <p class="filter-summary">{{ getFilterSummary() }}</p>

  <!-- Predefined tags display -->
  <div class="predefined-tags-display" *ngIf="currentFilter.settore || currentFilter.attivita || currentFilter.professione">
    <span class="tag-chip predefined" *ngIf="currentFilter.settore">
      {{ 'TAG_FILTER.SECTOR' | translate }}: {{ currentFilter.settore }}
    </span>
    <span class="tag-chip predefined" *ngIf="currentFilter.attivita">
      {{ 'TAG_FILTER.ACTIVITY' | translate }}: {{ currentFilter.attivita }}
    </span>
    <span class="tag-chip predefined" *ngIf="currentFilter.professione">
      {{ 'TAG_FILTER.PROFESSION' | translate }}: {{ currentFilter.professione }}
    </span>
    <span class="tag-chip universal">{{ 'TAG_FILTER.UNIVERSAL' | translate }}</span>
  </div>

  <!-- Custom tags display -->
  <div class="custom-tags-display" *ngIf="currentFilter.customTags.length > 0">
    <span
      *ngFor="let tag of currentFilter.customTags"
      class="tag-chip custom"
      (click)="removeCustomTag(tag)">
      {{ tag }} ✕
    </span>
  </div>
</div>

<!-- Tag hierarchy selection (using assign-tag-dialog structure) -->
<div class="tag-selection-content" *ngIf="isFilteringEnabled && !isLoading">
  <h4>{{ 'TAG_FILTER.AVAILABLE_TAGS' | translate }}</h4>

  <div *ngFor="let sector of sectorHierarchy">
    <mat-accordion>
      <mat-expansion-panel
        class="dialog-accordion-panel"
        [hideToggle]="!sector.subTags?.length"
        [ngClass]="{'unclickable-panel': !sector.subTags?.length}">

        <mat-expansion-panel-header>
          <mat-panel-title class="dialog-accordion-title">
            <mat-slide-toggle
              class="dialog-toggle"
              [checked]="isTagActive(sector.keyTag)"
              [disabled]="isTagPredefined(sector.keyTag)"
              (change)="toggleTagSelection(sector.keyTag, $event)"
              (click)="$event.stopPropagation()">
            </mat-slide-toggle>
            {{ sector.description }}
            <span *ngIf="isTagPredefined(sector.keyTag)" class="predefined-indicator">🔒</span>
          </mat-panel-title>
        </mat-expansion-panel-header>

        <!-- Attività -->
        <mat-accordion *ngIf="sector.subTags?.length">
          <mat-expansion-panel
            class="dialog-accordion-panel"
            *ngFor="let activity of sector.subTags"
            [hideToggle]="!activity.subTags?.length"
            [ngClass]="{'unclickable-panel': !activity.subTags?.length}">

            <mat-expansion-panel-header>
              <mat-panel-title class="dialog-accordion-title">
                <mat-slide-toggle
                  class="dialog-toggle"
                  [checked]="isTagActive(activity.keyTag)"
                  [disabled]="isTagPredefined(activity.keyTag)"
                  (change)="toggleTagSelection(activity.keyTag, $event)"
                  (click)="$event.stopPropagation()">
                </mat-slide-toggle>
                {{ activity.description }}
                <span *ngIf="isTagPredefined(activity.keyTag)" class="predefined-indicator">🔒</span>
              </mat-panel-title>
            </mat-expansion-panel-header>

            <!-- Professioni -->
            <div *ngIf="activity.subTags?.length">
              <p *ngFor="let profession of activity.subTags">
                <mat-slide-toggle
                  class="dialog-toggle profession-toggle"
                  [checked]="isTagActive(profession.keyTag)"
                  [disabled]="isTagPredefined(profession.keyTag)"
                  (change)="toggleTagSelection(profession.keyTag, $event)"
                  (click)="$event.stopPropagation()">
                </mat-slide-toggle>
                <span class="toggle-profession-label">
                  {{ profession.description }}
                  <span *ngIf="isTagPredefined(profession.keyTag)" class="predefined-indicator">🔒</span>
                </span>
              </p>
            </div>
          </mat-expansion-panel>
        </mat-accordion>
      </mat-expansion-panel>
    </mat-accordion>
  </div>
</div>

<!-- No filter state -->
<div class="no-filter-state" *ngIf="!isFilteringEnabled">
  <p>{{ 'TAG_FILTER.FILTERING_DISABLED' | translate }}</p>
  <p class="help-text">{{ 'TAG_FILTER.FILTERING_DISABLED_HELP' | translate }}</p>
</div>
