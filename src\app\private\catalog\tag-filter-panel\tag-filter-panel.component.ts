import { <PERSON>mpo<PERSON>, <PERSON>Ini<PERSON>, <PERSON><PERSON><PERSON><PERSON>, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { Subject, takeUntil } from 'rxjs';
import { TagFilterService } from '../../services/tag-filter.service';
import { CatalogsService } from '../../services/catalog.service';
import {
  CustomerTagFilter,
  TagFilterState,
  Tag,
  TypologyContent
} from '../../model/sector-hierarchy-response';

// Material imports (same as assign-tag-dialog)
import { MatButtonModule } from '@angular/material/button';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSlideToggleChange } from '@angular/material/slide-toggle';

@Component({
  selector: 'app-tag-filter-panel',
  templateUrl: './tag-filter-panel.component.html',
  styleUrls: ['./tag-filter-panel.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    TranslateModule,
    MatButtonModule,
    MatExpansionModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatSlideToggleModule,
    MatProgressSpinnerModule
  ]
})
export class TagFilterPanelComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  
  // Services
  private _tagFilterService = inject(TagFilterService);
  private _catalogsService = inject(CatalogsService);

  // State
  currentFilter: CustomerTagFilter | null = null;
  isFilteringEnabled = false;
  isLoading = false;

  // Tag hierarchy data
  sectorHierarchy: Tag[] = [];
  availableTags: Tag[] = [];

  // UI state
  isExpanded = false;
  selectedCustomTags: string[] = [];

  ngOnInit() {
    this.initializeComponent();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private async initializeComponent() {
    try {
      // Subscribe to filter state changes
      this._tagFilterService.filterState$
        .pipe(takeUntil(this.destroy$))
        .subscribe(state => {
          this.updateComponentState(state);
        });

      // Load sector hierarchy
      await this.loadSectorHierarchy();

    } catch (error) {
      console.error('❌ Error initializing tag filter panel:', error);
    }
  }

  private updateComponentState(state: TagFilterState) {
    this.currentFilter = state.currentFilter;
    this.isFilteringEnabled = state.isFilteringEnabled;
    
    if (this.currentFilter) {
      this.selectedCustomTags = [...this.currentFilter.customTags];
    }
  }

  private async loadSectorHierarchy() {
    try {
      this.isLoading = true;
      
      // TODO: Get catalog ID from configuration
      const catalogId = 1;
      
      this._catalogsService.getSectorHierarchy({ idCatalog: catalogId })
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (content: TypologyContent) => {
            this.sectorHierarchy = content.tags || [];
            this.flattenTagHierarchy();
          },
          error: (error) => {
            console.error('❌ Error loading sector hierarchy:', error);
          },
          complete: () => {
            this.isLoading = false;
          }
        });

    } catch (error) {
      console.error('❌ Error loading sector hierarchy:', error);
      this.isLoading = false;
    }
  }

  private flattenTagHierarchy() {
    this.availableTags = [];
    this.flattenTags(this.sectorHierarchy);
  }

  private flattenTags(tags: Tag[]) {
    for (const tag of tags) {
      this.availableTags.push(tag);
      if (tag.subTags && tag.subTags.length > 0) {
        this.flattenTags(tag.subTags);
      }
    }
  }

  /**
   * Toggle the filtering on/off
   */
  async toggleFiltering() {
    try {
      this._tagFilterService.setFilteringEnabled(!this.isFilteringEnabled);
    } catch (error) {
      console.error('❌ Error toggling filtering:', error);
    }
  }

  /**
   * Toggle tag selection (used by mat-slide-toggle)
   */
  async toggleTagSelection(keyTag: string, event: MatSlideToggleChange) {
    try {
      if (event.checked) {
        await this.addCustomTag(keyTag);
      } else {
        await this.removeCustomTag(keyTag);
      }
    } catch (error) {
      console.error('❌ Error toggling tag selection:', error);
    }
  }

  /**
   * Add a custom tag to the filter
   */
  async addCustomTag(keyTag: string) {
    try {
      if (!this.canAddTag(keyTag)) {
        return;
      }

      await this._tagFilterService.addCustomTag(keyTag);
      console.log(`✅ Added custom tag: ${keyTag}`);

    } catch (error) {
      console.error('❌ Error adding custom tag:', error);
    }
  }

  /**
   * Remove a custom tag from the filter
   */
  async removeCustomTag(keyTag: string) {
    try {
      if (!this.canRemoveTag(keyTag)) {
        console.warn(`Cannot remove predefined tag: ${keyTag}`);
        return;
      }

      await this._tagFilterService.removeCustomTag(keyTag);
      console.log(`✅ Removed custom tag: ${keyTag}`);

    } catch (error) {
      console.error('❌ Error removing custom tag:', error);
    }
  }

  /**
   * Check if a tag can be added
   */
  private canAddTag(keyTag: string): boolean {
    if (!this.currentFilter) {
      return false;
    }

    // Check if tag is already in custom tags
    if (this.currentFilter.customTags.includes(keyTag)) {
      return false;
    }

    // Check if tag is already a predefined tag
    const predefinedTags = [
      this.currentFilter.settore,
      this.currentFilter.attivita,
      this.currentFilter.professione,
      'UNIVERSALE'
    ].filter(Boolean);

    return !predefinedTags.includes(keyTag);
  }

  /**
   * Check if a tag can be removed
   */
  private canRemoveTag(keyTag: string): boolean {
    if (!this.currentFilter) {
      return false;
    }

    // Check if it's a predefined tag (cannot be removed)
    const predefinedTags = [
      this.currentFilter.settore,
      this.currentFilter.attivita,
      this.currentFilter.professione,
      'UNIVERSALE'
    ].filter(Boolean);

    return !predefinedTags.includes(keyTag);
  }

  /**
   * Check if a tag is currently active
   */
  isTagActive(keyTag: string): boolean {
    if (!this.currentFilter) {
      return false;
    }

    const allActiveTags = [
      this.currentFilter.settore,
      this.currentFilter.attivita,
      this.currentFilter.professione,
      'UNIVERSALE',
      ...this.currentFilter.customTags
    ].filter(Boolean);

    return allActiveTags.includes(keyTag);
  }

  /**
   * Check if a tag is predefined (cannot be removed)
   */
  isTagPredefined(keyTag: string): boolean {
    if (!this.currentFilter) {
      return false;
    }

    const predefinedTags = [
      this.currentFilter.settore,
      this.currentFilter.attivita,
      this.currentFilter.professione,
      'UNIVERSALE'
    ].filter(Boolean);

    return predefinedTags.includes(keyTag);
  }

  /**
   * Get the display name for a tag level
   */
  getTagLevelName(level: number): string {
    switch (level) {
      case 1: return 'Professione';
      case 2: return 'Attività';
      case 3: return 'Settore';
      default: return 'Tag';
    }
  }

  /**
   * Toggle the panel expansion
   */
  toggleExpansion() {
    this.isExpanded = !this.isExpanded;
  }

  /**
   * Get current filter summary for display
   */
  getFilterSummary(): string {
    if (!this.currentFilter) {
      return 'Nessun filtro attivo';
    }

    const activeTags = [
      this.currentFilter.settore,
      this.currentFilter.attivita,
      this.currentFilter.professione,
      ...this.currentFilter.customTags
    ].filter(Boolean);

    if (activeTags.length === 0) {
      return 'Solo tag Universale';
    }

    return `${activeTags.length} tag attivi`;
  }
}
